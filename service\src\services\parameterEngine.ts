import fs from 'fs'
import path from 'path'
import { ExternalApiService, ParameterData, ParameterConfig } from './externalApi'

// 类型定义
export interface ContentReplaceRequest {
  templateId: string
  parameterDataId: string
}

export interface ContentReplaceResult {
  modifiedTemplateData: any
  previewUrl: string
}

export class ParameterEngineService {
  constructor(private externalApi: ExternalApiService) {}

  /**
   * 替换内容
   * @param request 替换请求
   * @returns 替换结果
   */
  async replaceContent(request: ContentReplaceRequest): Promise<ContentReplaceResult> {
    console.log(`[ParameterEngine] Starting content replacement for template ${request.templateId}, data ${request.parameterDataId}`)

    try {
      // 1. 获取模板数据
      const templateData = await this.getTemplateData(request.templateId)

      // 2. 获取参数数据
      const parameterData = await this.externalApi.getParameterData(request.parameterDataId)

      // 3. 获取参数配置
      const parameterConfig = await this.externalApi.getParameterConfig(parameterData.configId)

      // 4. 执行内容替换
      const modifiedTemplateData = this.replaceTextContent(templateData, parameterData, parameterConfig)

      // 5. 生成预览URL
      const previewUrl = this.generatePreviewUrl(request.parameterDataId)

      console.log(`[ParameterEngine] Content replacement completed successfully`)

      return {
        modifiedTemplateData,
        previewUrl
      }
    } catch (error) {
      console.error(`[ParameterEngine] Content replacement failed:`, error)
      throw new Error(`Failed to replace content: ${error.message}`)
    }
  }

  /**
   * 获取模板数据
   * @param templateId 模板ID
   * @returns 模板数据
   */
  private async getTemplateData(templateId: string): Promise<any> {
    try {
      const templatePath = path.resolve(__dirname, `../mock/templates/${templateId}.json`)
      const templateData = fs.readFileSync(templatePath, 'utf8')
      return JSON.parse(templateData)
    } catch (error) {
      throw new Error(`Failed to load template ${templateId}: ${error.message}`)
    }
  }

  /**
   * 替换文本内容
   * @param templateData 模板数据
   * @param parameterData 参数数据
   * @param config 参数配置
   * @returns 修改后的模板数据
   */
  private replaceTextContent(templateData: any, parameterData: ParameterData, config: ParameterConfig): any {
    console.log(`[ParameterEngine] Replacing text content with ${config.parameters.length} parameters`)

    // 深拷贝模板数据
    const modifiedData = JSON.parse(JSON.stringify(templateData))
    let content = JSON.parse(modifiedData.data)

    // 处理多页面格式
    if (Array.isArray(content)) {
      content = content.map(page => this.replacePageContent(page, parameterData, config))
    } else {
      content = this.replacePageContent(content, parameterData, config)
    }

    modifiedData.data = JSON.stringify(content)
    return modifiedData
  }

  /**
   * 替换页面内容
   * @param pageData 页面数据
   * @param parameterData 参数数据
   * @param config 参数配置
   * @returns 修改后的页面数据
   */
  private replacePageContent(pageData: any, parameterData: ParameterData, config: ParameterConfig): any {
    const layers = pageData.layers || pageData.widgets || []
    let replacedCount = 0

    layers.forEach((layer: any) => {
      if (layer.type === 'w-text') {
        // 查找对应的参数定义
        const paramDef = config.parameters.find(p => p.elementUuid === layer.uuid)
        if (paramDef && paramDef.isEnabled) {
          const newValue = parameterData.parameterValues[paramDef.parameterName]
          if (newValue !== undefined) {
            // 替换文本内容，保持HTML格式
            const oldText = layer.text
            layer.text = this.formatTextContent(newValue, layer.text)
            console.log(`[ParameterEngine] Replaced text in element ${layer.uuid}: "${oldText}" -> "${layer.text}"`)
            replacedCount++
          }
        }
      }
    })

    console.log(`[ParameterEngine] Replaced ${replacedCount} text elements in page`)
    return pageData
  }

  /**
   * 格式化文本内容
   * @param newValue 新值
   * @param originalText 原始文本
   * @returns 格式化后的文本
   */
  private formatTextContent(newValue: string, originalText: string): string {
    // 如果原文本包含HTML标签，保持格式
    if (originalText.includes('<br/>') || originalText.includes('<br>')) {
      // 将换行符转换为<br/>标签
      return newValue.replace(/\n/g, '<br/>')
    }

    return newValue
  }

  /**
   * 生成预览URL
   * @param parameterDataId 参数数据ID
   * @returns 预览URL
   */
  private generatePreviewUrl(parameterDataId: string): string {
    return `/preview/parameter/${parameterDataId}`
  }
}
