{"id": "1", "data": "[{\"global\":{\"name\":\"作品名\",\"type\":\"page\",\"uuid\":\"-1\",\"left\":0,\"top\":0,\"width\":1242,\"height\":2208,\"backgroundColor\":\"#f2eae7ff\",\"backgroundImage\":\"\",\"opacity\":1,\"tag\":0,\"setting\":[],\"record\":{}},\"layers\":[{\"name\":\"图片\",\"type\":\"w-image\",\"uuid\":\"823d4893d010\",\"width\":340,\"height\":291,\"left\":48,\"top\":1848,\"zoom\":1,\"transform\":\"scale(1)translate(0px, 0px)\",\"radius\":0,\"opacity\":1,\"parent\":\"-1\",\"imgUrl\":\"http://127.0.0.1:7001/store/2.png\",\"setting\":[],\"record\":{\"width\":0,\"height\":0,\"minWidth\":10,\"minHeight\":10,\"dir\":\"all\"},\"letterSpacing\":null,\"rotate\":0,\"imageTransform\":{\"a\":1,\"b\":0,\"c\":0,\"d\":1,\"tx\":0,\"ty\":0},\"filter\":{\"contrast\":0,\"sharpness\":0,\"hueRotate\":0,\"saturate\":0,\"brightness\":0,\"gaussianBlur\":0,\"temperature\":0,\"tint\":0},\"naturalDuration\":0},{\"name\":\"文本\",\"type\":\"w-text\",\"uuid\":\"4923988c39e9\",\"editable\":false,\"left\":275.33,\"top\":1475.17,\"transform\":\"\",\"lineHeight\":1.56,\"letterSpacing\":0,\"fontSize\":40.66682352941177,\"fontClass\":{\"alias\":\"站酷快乐体\",\"id\":543,\"value\":\"zcool-kuaile-regular\",\"url\":\"https://lib.baomitu.com/fonts/zcool-kuaile/zcool-kuaile-regular.woff2\"},\"fontWeight\":400,\"fontStyle\":\"normal\",\"writingMode\":\"horizontal-tb\",\"textDecoration\":\"none\",\"color\":\"#d95151ff\",\"textAlign\":\"center\",\"text\":\"模板显示效果为虚拟<br/>请从PSD导入模板开始；<br/>添加模板后，<br/>再编辑下载；<br/>随意的测试数据<br/>该模板显示效果无参考性质\",\"opacity\":1,\"backgroundColor\":\"\",\"parent\":\"-1\",\"record\":{\"width\":691,\"height\":381,\"minWidth\":40.66682352941177,\"minHeight\":63.440244705882364,\"dir\":\"horizontal\"},\"width\":\"691.34\",\"height\":381,\"imgUrl\":\"\",\"rotate\":0,\"filter\":{\"contrast\":0,\"sharpness\":0,\"hueRotate\":0,\"saturate\":0,\"brightness\":0,\"gaussianBlur\":0,\"temperature\":0,\"tint\":0}},{\"name\":\"文本\",\"type\":\"w-text\",\"uuid\":\"59d5880bf47b\",\"editable\":false,\"left\":307.96000000000004,\"top\":774.8862351685095,\"transform\":\"\",\"lineHeight\":1.07,\"letterSpacing\":0,\"fontSize\":150,\"fontClass\":{\"alias\":\"站酷快乐体\",\"id\":543,\"value\":\"zcool-kuaile-regular\",\"url\":\"https://lib.baomitu.com/fonts/zcool-kuaile/zcool-kuaile-regular.woff2\"},\"fontWeight\":400,\"fontStyle\":\"normal\",\"writingMode\":\"horizontal-tb\",\"textDecoration\":\"none\",\"color\":\"#d95151ff\",\"textAlign\":\"center\",\"text\":\"前端出图<br/>直接下载\",\"opacity\":1,\"backgroundColor\":\"\",\"parent\":\"-1\",\"record\":{\"width\":0,\"height\":0,\"minWidth\":0,\"minHeight\":0,\"dir\":\"horizontal\"},\"width\":627.08,\"height\":322,\"imgUrl\":\"\",\"rotate\":0,\"filter\":{\"contrast\":0,\"sharpness\":0,\"hueRotate\":0,\"saturate\":0,\"brightness\":0,\"gaussianBlur\":0,\"temperature\":0,\"tint\":0}},{\"name\":\"文本\",\"type\":\"w-text\",\"uuid\":\"bd5d46685e3e\",\"editable\":false,\"left\":369.48,\"top\":692.77,\"transform\":\"\",\"lineHeight\":1.2,\"letterSpacing\":-2,\"fontSize\":48,\"fontClass\":{\"alias\":\"站酷快乐体\",\"id\":543,\"value\":\"zcool-kuaile-regular\",\"url\":\"https://lib.baomitu.com/fonts/zcool-kuaile/zcool-kuaile-regular.woff2\"},\"fontWeight\":400,\"fontStyle\":\"normal\",\"writingMode\":\"horizontal-tb\",\"textDecoration\":\"none\",\"color\":\"#f59595ff\",\"textAlign\":\"center\",\"text\":\"Happybirthday\",\"opacity\":1,\"backgroundColor\":\"\",\"parent\":\"-1\",\"record\":{\"width\":503,\"height\":58,\"minWidth\":48,\"minHeight\":57.599999999999994,\"dir\":\"horizontal\"},\"width\":\"503.04\",\"height\":58,\"imgUrl\":\"\",\"rotate\":0,\"filter\":{\"contrast\":0,\"sharpness\":0,\"hueRotate\":0,\"saturate\":0,\"brightness\":0,\"gaussianBlur\":0,\"temperature\":0,\"tint\":0}},{\"name\":\"文本\",\"type\":\"w-text\",\"uuid\":\"a0a7880aceb0\",\"editable\":false,\"left\":163.49999999999997,\"top\":863.3862351685095,\"transform\":\"\",\"lineHeight\":1.2,\"letterSpacing\":-5.999999999999999,\"fontSize\":120,\"fontClass\":{\"alias\":\"站酷快乐体\",\"id\":543,\"value\":\"zcool-kuaile-regular\",\"url\":\"https://lib.baomitu.com/fonts/zcool-kuaile/zcool-kuaile-regular.woff2\"},\"fontWeight\":400,\"fontStyle\":\"normal\",\"writingMode\":\"horizontal-tb\",\"textDecoration\":\"none\",\"color\":\"#d95151ff\",\"textAlign\":\"center\",\"text\":\"HAPPYBIRTHDAY\",\"opacity\":0.1411764705882353,\"backgroundColor\":\"\",\"parent\":\"-1\",\"record\":{\"width\":0,\"height\":0,\"minWidth\":0,\"minHeight\":0,\"dir\":\"horizontal\"},\"width\":908.8,\"height\":145,\"imgUrl\":\"\",\"rotate\":0,\"filter\":{\"contrast\":0,\"sharpness\":0,\"hueRotate\":0,\"saturate\":0,\"brightness\":0,\"gaussianBlur\":0,\"temperature\":0,\"tint\":0}},{\"name\":\"文本\",\"type\":\"w-text\",\"uuid\":\"b474012aa064\",\"editable\":false,\"left\":413,\"top\":1168.83,\"transform\":\"\",\"lineHeight\":1.2,\"letterSpacing\":0,\"fontSize\":41.6,\"fontClass\":{\"alias\":\"站酷快乐体\",\"id\":543,\"value\":\"zcool-kuaile-regular\",\"url\":\"https://lib.baomitu.com/fonts/zcool-kuaile/zcool-kuaile-regular.woff2\"},\"fontWeight\":400,\"fontStyle\":\"normal\",\"writingMode\":\"horizontal-tb\",\"textDecoration\":\"none\",\"color\":\"#d95151ff\",\"textAlign\":\"center\",\"text\":\"Happybirthday\",\"opacity\":1,\"backgroundColor\":\"\",\"parent\":\"-1\",\"record\":{\"width\":416,\"height\":50,\"minWidth\":41.6,\"minHeight\":49.92,\"dir\":\"horizontal\"},\"width\":416,\"height\":50,\"imgUrl\":\"\",\"rotate\":0,\"filter\":{\"contrast\":0,\"sharpness\":0,\"hueRotate\":0,\"saturate\":0,\"brightness\":0,\"gaussianBlur\":0,\"temperature\":0,\"tint\":0}},{\"name\":\"文本\",\"type\":\"w-text\",\"uuid\":\"0692dea9191f\",\"editable\":false,\"left\":\"453.21\",\"top\":1224.02,\"transform\":\"\",\"lineHeight\":1.2,\"letterSpacing\":-2.5,\"fontSize\":56,\"fontClass\":{\"alias\":\"站酷快乐体\",\"id\":543,\"value\":\"zcool-kuaile-regular\",\"url\":\"https://lib.baomitu.com/fonts/zcool-kuaile/zcool-kuaile-regular.woff2\"},\"fontWeight\":400,\"fontStyle\":\"normal\",\"writingMode\":\"horizontal-tb\",\"textDecoration\":\"none\",\"color\":\"#d95151ff\",\"textAlign\":\"center\",\"text\":\"演示从前端出图的模式\",\"opacity\":1,\"backgroundColor\":\"\",\"parent\":\"-1\",\"record\":{\"width\":336,\"height\":67,\"minWidth\":56,\"minHeight\":67.2,\"dir\":\"horizontal\"},\"width\":\"335.59\",\"height\":67,\"imgUrl\":\"\",\"rotate\":0,\"filter\":{\"contrast\":0,\"sharpness\":0,\"hueRotate\":0,\"saturate\":0,\"brightness\":0,\"gaussianBlur\":0,\"temperature\":0,\"tint\":0}}]}]", "title": "示例模板 - 日签插画手机海报", "width": 1242, "height": 2208}