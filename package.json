{"name": "xunpai-design", "version": "1.0.0", "private": true, "author": "<PERSON><PERSON><PERSON>", "scripts": {"prepared": "npm i && cd service && PUPPETEER_DOWNLOAD_BASE_URL=https://cdn.npmmirror.com/binaries/chrome-for-testing npm i", "serve": "npm run dev & cd service && npm run dev", "dev": "cross-env NODE_ENV=development vite", "build": "cross-env NODE_ENV=production && vite build", "v-build": "cross-env NODE_ENV=production && vite build", "v-build-hard": "cross-env NODE_ENV=production vue-tsc --noEmit && vite build"}, "dependencies": {"@palxp/color-picker": "workspace:*", "@palxp/image-extraction": "workspace:*", "@scena/guides": "^0.18.1", "axios": "^0.21.1", "core-js": "^3.6.5", "cropperjs": "^1.6.1", "dayjs": "^1.10.7", "element-plus": "^2.6.3", "fontfaceobserver": "^2.1.0", "html2canvas": "^1.0.0", "immer": "^10.0.4", "microdiff": "^1.4.0", "mitt": "^3.0.1", "moveable": "^0.26.0", "moveable-helper": "^0.4.0", "nanoid": "^3.1.23", "normalize.css": "^8.0.1", "pinia": "^2.1.7", "psd.js": "^3.9.0", "qr-code-styling": "^1.6.0-rc.1", "selecto": "^1.13.0", "throttle-debounce": "^3.0.1", "vite-plugin-compression": "^0.5.1", "vue": "3.4.19", "vue-i18n": "^9.13.1", "vue-router": "^4.0.0-0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/cropperjs": "^1.3.0", "@types/fontfaceobserver": "^2.1.3", "@types/node": "^20.11.24", "@types/throttle-debounce": "^2.1.0", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.3.1", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-alloy": "~4.1.0", "eslint-plugin-vue": "^7.12.1", "less": "^4.1.1", "terser": "^5.28.1", "typescript": "^5.2.2", "unplugin-element-plus": "^0.7.1", "vite": "^5.1.4", "vue-tsc": "^1.8.27"}, "workspaces": ["packages/*"], "browserslist": ["Chrome >= 90"], "website": "https://design.palxp.cn", "homepage": "https://xp.palxp.cn"}