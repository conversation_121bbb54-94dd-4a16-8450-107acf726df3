<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-07 23:50:21
 * @Description:
 * @LastEditors: <PERSON><PERSON><PERSON> <https://m.palxp.cn>
 * @LastEditTime: 2023-10-08 16:02:58
-->

<img style="display: inline-block;" src="https://img.shields.io/github/watchers/palxiao/front-end-arsenal?style=social" /> <img style="display: inline-block;" src="https://img.shields.io/github/forks/palxiao/front-end-arsenal?style=social" /> <img style="display: inline-block;" src="https://img.shields.io/github/stars/palxiao/front-end-arsenal?style=social" />

# image-extraction

> TODO:

<img style="display: inline-block;" src="https://img.shields.io/npm/v/@palxp/image-extraction" /> <img style="display: inline-block;" src="https://img.shields.io/bundlephobia/min/@palxp/image-extraction?color=%2344cc88" /> <img style="display: inline-block;" src="https://img.shields.io/npm/dm/@palxp/image-extraction" />

## Usage

```
yarn add @palxp/image-extraction

import image-extraction from '@palxp/image-extraction'
```

## API

[API Docs 链接](/#/docs)

  <iframe src="/#/docs/image-extraction/-image-extraction?preview=true" frameborder="0"></iframe>
