# 动态参数模板系统测试计划

## 测试范围

### 迅排设计提供的API接口

| API接口 | 方法 | 路径 | 功能描述 |
|---------|------|------|----------|
| 模板解析 | POST | /api/template/parse | 解析模板内容，生成参数候选项 |
| 参数化预览 | POST | /api/parameter/preview | 生成参数化预览 |
| 预览页面 | GET | /preview/parameter/:dataId | 返回预览页面HTML |
| 参数化截图 | GET | /api/screenshots | 生成参数化图片（扩展功能） |
| 批量生成 | POST | /api/parameter/batch-generate | 批量生成图片 |
| 批量状态查询 | GET | /api/parameter/batch-status/:batchId | 查询批量任务状态 |

### 主项目需要实现的API接口（模拟API）

| API接口 | 方法 | 路径 | 功能描述 |
|---------|------|------|----------|
| 获取参数数据 | GET | /api/external/parameter-data/:dataId | 获取用户填写的参数数据 |
| 获取参数配置 | GET | /api/external/parameter-config/:configId | 获取模板的参数配置 |
| 健康检查 | GET | /api/external/health | API服务健康检查 |

## 测试用例类型

### 1. 正常流程测试
- 使用有效数据测试API正常功能
- 验证响应格式和数据正确性
- 测试成功场景的完整流程

### 2. 异常处理测试
- 缺失必需参数
- 无效参数值
- 不存在的资源ID
- 外部API调用失败
- 网络超时和连接错误

### 3. 边界条件测试
- 空值和null值
- 超长字符串
- 特殊字符（中文、HTML标签、SQL注入等）
- 数值边界（最大值、最小值、零值）
- 大数据量处理

### 4. 集成流程测试
- 模板解析 → 参数替换 → 预览生成 → 图片生成
- 多个API协作的完整业务流程
- 数据一致性验证

### 5. 性能测试
- 响应时间测试
- 并发请求测试
- 大批量数据处理测试
- 内存和CPU使用率监控

## 测试数据准备

### 有效测试数据
```json
{
  "templateId": "2",
  "parameterDataId": "user-data-123",
  "configId": "config-456",
  "batchId": "batch_1234567890_abc123"
}
```

### 无效测试数据
```json
{
  "invalidTemplateId": "999",
  "invalidDataId": "invalid-data",
  "invalidConfigId": "invalid-config",
  "emptyString": "",
  "nullValue": null,
  "specialChars": "<script>alert('xss')</script>",
  "longString": "a".repeat(10000)
}
```

### 边界值数据
```json
{
  "chineseText": "你好世界测试数据",
  "htmlTags": "<div>HTML标签测试</div>",
  "sqlInjection": "'; DROP TABLE users; --",
  "unicodeChars": "🎉🚀💻🔥",
  "numberAsString": "123456789",
  "booleanAsString": "true"
}
```

## 测试环境要求

### 服务端口配置
- 迅排设计服务：http://localhost:7001
- 模拟API服务：http://localhost:3000
- 前端开发服务：http://localhost:5174

### 依赖服务
- Node.js 环境
- 模板文件（template ID=2）
- 模拟参数数据和配置

### 测试工具
- PowerShell Invoke-WebRequest
- 浏览器开发者工具
- 性能监控工具

## 预期测试结果

### 成功标准
- 所有正常流程测试通过率 100%
- 异常处理测试覆盖率 >= 90%
- API响应时间 < 5秒
- 批量处理成功率 >= 95%
- 无内存泄漏和资源泄漏

### 质量指标
- 功能测试通过率 >= 95%
- 性能测试达标率 >= 90%
- 安全测试无高危漏洞
- 兼容性测试通过主流浏览器
