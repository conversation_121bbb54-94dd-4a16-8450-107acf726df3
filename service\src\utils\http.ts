/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-01-25 17:02:02
 * @Description:
 * @LastEditors: <PERSON><PERSON><PERSON> <https://m.palxp.cn>
 * @LastEditTime: 2024-08-12 13:59:34
 */
import axios from 'axios'

const httpRequest = axios.create({
  maxContentLength: Infinity,
  maxBodyLength: Infinity,
})

httpRequest.interceptors.response.use((config: any) => {
  return config.data
})

export default httpRequest
