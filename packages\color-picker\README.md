<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-29 22:54:18
 * @Description:
 * @LastEditors: <PERSON><PERSON><PERSON> <https://m.palxp.cn>
 * @LastEditTime: 2024-01-31 10:51:35
-->
<img style="display: inline-block;" src="https://img.shields.io/github/watchers/palxiao/front-end-arsenal?style=social" />
<img style="display: inline-block;" src="https://img.shields.io/github/forks/palxiao/front-end-arsenal?style=social" />
<img style="display: inline-block;" src="https://img.shields.io/github/stars/palxiao/front-end-arsenal?style=social" />

# color-picker

> TODO: 颜色取色器，适用于 Vue3

<img style="display: inline-block;" src="https://img.shields.io/npm/v/@palxp/color-picker" />
<img style="display: inline-block;" src="https://img.shields.io/bundlephobia/min/@palxp/color-picker?color=%2344cc88" />
<img style="display: inline-block;" src="https://img.shields.io/npm/dm/@palxp/color-picker" />

## Usage

```
yarn add @palxp/color-picker

import colorPicker from '@palxp/color-picker'
```

## API

[API Docs 链接](/#/docs)

  <iframe src="/#/docs/color-picker/index?preview=true" frameborder="0"></iframe>
