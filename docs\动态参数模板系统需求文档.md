# 动态参数模板系统需求文档

## 1. 需求概述

### 1.1 项目背景
迅排设计作为一个在线海报设计平台，目前主要服务于有设计能力的用户。为了进一步降低使用门槛，让更多用户能够快速生成个性化设计作品，我们需要开发一个基于模板内容的动态参数系统。该系统通过智能解析模板中的文本内容，自动识别可参数化的元素，让管理员配置参数并生成用户填写表单，实现模板的快速个性化定制。

### 1.2 核心价值
- **智能内容识别**：自动解析模板中的文本内容，识别可参数化的元素
- **灵活参数配置**：管理员可自由选择开放哪些参数供用户填写
- **简化用户操作**：用户只需填写表单即可生成个性化作品
- **保持设计质量**：参数替换保持原有的文本样式和格式
- **提高复用效率**：一个模板可支持多种参数配置方案

### 1.3 目标用户
- **主项目开发者**：集成迅排设计参数化服务到自己的系统中
- **主项目管理员**：在主项目中配置模板参数，选择开放给用户的可填写字段
- **主项目终端用户**：通过主项目的表单界面快速生成个性化设计作品
- **企业客户**：通过主项目批量生成个性化营销物料
- **第三方开发者**：通过迅排设计API接口集成参数化模板功能

### 1.4 应用场景
- **个人名片**：替换姓名、职位、联系方式等个人信息
- **活动海报**：替换活动名称、时间、地点、联系方式等
- **产品宣传**：替换产品名称、价格、特性描述等
- **节日祝福**：替换祝福语、收件人姓名等个性化内容
- **企业宣传**：替换公司信息、地址、电话等企业信息

### 1.5 现有项目基础
在开始开发前，需要了解迅排设计项目的现有结构：

#### 1.5.1 项目目录结构
```
poster-design/
├── src/                    # 前端源码（Vue 3 + TypeScript）
│   ├── components/         # 组件
│   ├── views/              # 页面
│   ├── store/              # 状态管理（Pinia）
│   └── api/                # API接口
├── service/                # 后端服务（Node.js + TypeScript）
│   ├── src/
│   │   ├── service/        # 业务服务
│   │   ├── control/        # 路由控制
│   │   ├── mock/           # 模拟数据
│   │   │   └── templates/  # 模板数据文件
│   │   └── configs.ts      # 配置文件
│   └── package.json
└── docs/                   # 文档目录
```

#### 1.5.2 关键现有功能
- **模板管理**：`service/src/service/design.ts` - 模板CRUD操作
- **截图服务**：`service/src/service/screenshots.ts` - 基于Puppeteer的图片生成
- **文件管理**：`service/src/service/file.ts` - 文件上传下载
- **模板数据**：`service/src/mock/templates/` - JSON格式的模板数据
- **前端画布**：`src/components/modules/layout/designBoard/` - 设计画布组件

#### 1.5.3 现有API接口
- `GET /design/temp?id=xxx` - 获取模板详情
- `POST /design/edit` - 保存模板
- `GET /api/screenshots` - 生成截图
- `POST /api/file/upload` - 文件上传

#### 1.5.4 技术栈
- **前端**：Vue 3.4.19 + TypeScript + Vite + Element Plus
- **后端**：Node.js + TypeScript + Express + Puppeteer
- **数据存储**：文件系统（JSON文件）
- **图片生成**：Puppeteer + Html2canvas

## 2. 核心功能需求

### 2.1 模板内容解析功能

#### 2.1.1 模板数据解析 ❌ 新开发
- **模板数据获取**：
  - 调用现有模板详情接口 `GET /design/temp?id=2` 获取模板数据
  - 解析返回的JSON数据结构，提取 `data` 字段中的模板内容
  - 支持多页面模板的解析（`data` 为数组格式）

- **文本元素识别**：
  - 遍历模板数据中的 `layers` 数组
  - 识别所有 `type: "w-text"` 的文本元素
  - 提取文本元素的关键属性：`uuid`、`text`、`left`、`top`、`fontSize`、`color` 等
  - 支持富文本内容解析（包含 `<br/>` 等HTML标签）

- **参数候选项生成**：
  - 自动为每个文本元素生成参数候选项
  - 生成参数ID（基于元素uuid）和默认参数名称
  - 提取文本内容作为参数的默认值
  - 分析文本内容类型（如：电话号码、地址、日期等）

#### 2.1.2 参数内容分析 ❌ 新开发
- **文本内容分类**：
  - 电话号码识别：匹配 "电话：xxx" 格式
  - 地址信息识别：匹配 "地址：xxx" 格式
  - 日期时间识别：匹配日期格式
  - 普通文本识别：其他文本内容

- **参数名称建议**：
  - 根据文本内容自动建议参数名称
  - 如："电话：8888-8888888" → 建议参数名 "phone"
  - 如："地址：广州市高林路888号" → 建议参数名 "address"
  - 如："你好,十二月" → 建议参数名 "greeting_text"

### 2.2 迅排设计服务功能（服务提供方）

#### 2.2.1 模板解析API服务 ❌ 新开发
- **模板解析接口**：
  - 提供 `POST /api/template/parse` 接口
  - 接收模板ID，返回解析结果和参数候选项
  - 支持多页面模板解析
  - 返回标准化的参数候选项数据

- **参数候选项智能生成**：
  - 自动分析文本内容类型
  - 生成建议的参数名称和标签
  - 提供参数类型建议（text、textarea、phone等）
  - 返回元素位置和样式信息

#### 2.2.2 参数化预览服务 ❌ 新开发
- **预览页面服务**：
  - 提供 `GET /preview/parameter/:dataId` 预览页面
  - 基于现有画布组件扩展参数化渲染
  - 支持实时内容替换和样式保持

- **预览数据生成**：
  - 提供 `POST /api/parameter/preview` 接口
  - 从主项目获取参数数据
  - 执行内容替换并生成预览URL

#### 2.2.3 外部API集成服务 ❌ 新开发
- **外部数据获取**：
  - 调用主项目API获取参数配置
  - 调用主项目API获取用户填写数据
  - 支持API重试和缓存机制
  - 提供连接健康检查

- **数据格式适配**：
  - 适配主项目的数据格式
  - 提供标准化的数据接口
  - 支持数据验证和错误处理

### 2.3 图片生成和导出功能（复用现有）

#### 2.3.1 参数化截图服务 ✅ 扩展现有
- **截图接口扩展**：
  - 扩展现有 `/api/screenshots` 接口
  - 新增 `parameterDataId` 参数支持
  - 保持现有的尺寸、质量等参数
  - 复用现有的Puppeteer截图逻辑

#### 2.3.2 批量生成服务 ❌ 新开发
- **批量处理接口**：
  - 提供 `POST /api/parameter/batch-generate` 接口
  - 支持批量参数数据处理
  - 基于现有队列系统实现
  - 提供批量状态查询接口

#### 2.3.3 文件管理服务 ✅ 完全复用
- **文件存储**：直接复用现有的文件存储机制
- **下载功能**：直接复用现有的文件下载功能
- **云端存储**：可选择复用现有的七牛云存储集成

## 3. 主项目集成需求（数据管理方）

### 3.1 主项目需要实现的功能

#### 3.1.1 参数配置管理功能
- **参数配置界面**：
  - 调用迅排设计解析API获取参数候选项
  - 提供参数属性设置界面（名称、标签、类型、验证规则等）
  - 支持参数开放控制（选择哪些参数对用户开放）
  - 参数配置的保存、编辑、删除功能

- **模板管理功能**：
  - 模板列表展示和选择
  - 参数配置与模板的关联管理
  - 支持配置版本管理和复制

#### 3.1.2 用户表单生成功能
- **动态表单生成**：
  - 根据参数配置生成用户填写表单
  - 支持不同参数类型的表单控件
  - 表单验证和错误提示
  - 表单数据的保存和管理

- **用户数据管理**：
  - 用户填写数据的CRUD操作
  - 支持草稿保存和正式提交
  - 用户数据的查询和导出

#### 3.1.3 外部API接口实现
- **为迅排设计提供的API**：
  - `GET /api/external/parameter-data/:dataId` - 获取参数数据
  - `GET /api/external/parameter-config/:configId` - 获取参数配置
  - `GET /api/external/health` - 健康检查接口

- **API安全和认证**：
  - API Key认证机制
  - 请求频率限制
  - 数据访问权限控制

### 3.2 主项目数据库设计

#### 3.2.1 数据库表结构（在主项目中）
```sql
-- 主项目数据库表设计
CREATE TABLE poster_template_configs (
  id VARCHAR(32) PRIMARY KEY,
  template_id VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
  template_title VARCHAR(255) COMMENT '模板标题',
  config_name VARCHAR(255) NOT NULL COMMENT '配置名称',
  config_description TEXT COMMENT '配置描述',
  parameters JSON NOT NULL COMMENT '参数定义JSON',
  created_by VARCHAR(32) COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status TINYINT DEFAULT 1 COMMENT '状态：1启用 0禁用'
);

CREATE TABLE poster_user_data (
  id VARCHAR(32) PRIMARY KEY,
  config_id VARCHAR(32) NOT NULL COMMENT '配置ID',
  user_id VARCHAR(32) COMMENT '用户ID',
  session_id VARCHAR(64) COMMENT '会话ID（匿名用户）',
  parameter_values JSON NOT NULL COMMENT '用户填写的参数值',
  is_draft BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
  preview_url VARCHAR(500) COMMENT '预览页面URL',
  generated_image_url VARCHAR(500) COMMENT '生成的图片URL',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 4. 技术实现方案

### 4.1 系统架构设计（API网关模式）

#### 4.1.1 整体架构
```
                    API网关模式架构
┌─────────────────────────────────────────────────────────────┐
│                      主项目                                 │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   管理员界面    │    │   用户界面      │                │
│  │  (参数配置)     │    │  (表单填写)     │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           └───────────────────────┼─────────────────────   │
│                                   │                        │
│              ┌─────────────────────┐                       │
│              │   主项目API         │                       │
│              │  (数据管理)         │                       │
│              └─────────────────────┘                       │
│                          │                                 │
│              ┌─────────────────────┐                       │
│              │   主项目数据库      │                       │
│              │  (参数配置+用户数据)│                       │
│              └─────────────────────┘                       │
└─────────────────────────────────────────────────────────────┘
                          │ HTTP API
                          ▼
┌─────────────────────────────────────────────────────────────┐
│                   迅排设计服务                              │
│              ┌─────────────────────┐                       │
│              │   参数化API         │                       │
│              │  (服务提供)         │                       │
│              └─────────────────────┘                       │
│                          │                                 │
│  ┌───────────────────────┼───────────────────────┐        │
│  │                   业务逻辑层                   │        │
│  │ ┌─────────────────┐   │ ┌─────────────────┐   │        │
│  │ │  模板解析引擎   │   │ │  参数替换引擎   │   │        │
│  │ │  (内容识别)     │   │ │  (内容替换)     │   │        │
│  │ └─────────────────┘   │ └─────────────────┘   │        │
│  │                       │                       │        │
│  │ ┌─────────────────┐   │ ┌─────────────────┐   │        │
│  │ │  外部API集成    │   │ │  现有截图服务   │   │        │
│  │ │  (数据获取)     │   │ │     (复用)      │   │        │
│  │ └─────────────────┘   │ └─────────────────┘   │        │
│  └───────────────────────┼───────────────────────┘        │
│                          │                                 │
│              ┌─────────────────────┐                       │
│              │   文件系统          │                       │
│              │  (模板数据+缓存)    │                       │
│              └─────────────────────┘                       │
└─────────────────────────────────────────────────────────────┘
```

#### 4.1.2 业务流程
```
主项目管理员 → 选择模板 → 调用迅排设计解析API → 获取参数候选项
     ↓
配置参数属性 → 保存到主项目数据库 → 生成配置ID
     ↓
主项目用户 → 填写表单 → 保存到主项目数据库 → 生成数据ID
     ↓
请求预览/生成 → 迅排设计调用主项目API → 获取参数数据 → 内容替换 → 生成图片
```

#### 4.1.3 迅排设计服务核心模块
- **模板解析引擎**：解析模板JSON数据，识别文本元素，生成参数候选项
- **外部API集成模块**：调用主项目API获取参数配置和用户数据
- **参数替换引擎**：将用户填写的内容替换到模板对应位置
- **预览渲染模块**：基于现有画布组件实现参数化预览
- **图片生成模块**：复用现有截图服务生成最终图片

#### 4.1.4 主项目核心模块
- **参数配置管理**：管理员配置参数属性，选择开放参数
- **用户表单生成**：根据参数配置动态生成用户填写表单
- **数据管理模块**：参数配置和用户数据的CRUD操作
- **外部API提供**：为迅排设计提供数据访问接口

### 4.2 迅排设计服务数据结构

#### 4.2.1 迅排设计服务不使用数据库
在API网关模式下，迅排设计服务作为纯服务提供方，不存储业务数据：
- **模板数据**：继续使用现有的文件系统存储（`service/src/mock/templates/`）
- **参数配置**：由主项目存储和管理
- **用户数据**：由主项目存储和管理
- **缓存数据**：使用内存缓存提升API调用性能

#### 4.2.2 TypeScript 类型定义

##### 迅排设计服务类型定义
```typescript
// 模板解析结果（迅排设计服务返回）
interface TemplateParseResult {
  templateId: string
  templateTitle: string
  textElements: TextElement[]
  parameterCandidates: ParameterCandidate[]
}

// 文本元素信息
interface TextElement {
  uuid: string
  type: 'w-text'
  text: string
  position: ElementPosition
  style: ElementStyle
}

interface ElementPosition {
  left: number
  top: number
  width: number
  height: number
}

interface ElementStyle {
  fontSize: number
  color: string
  fontFamily: string
  textAlign: string
  lineHeight: number
  letterSpacing: number
}

// 参数候选项（迅排设计服务生成）
interface ParameterCandidate {
  elementUuid: string
  suggestedName: string
  suggestedLabel: string
  suggestedType: ParameterType
  originalText: string
  textCategory: TextCategory
}

// 参数类型枚举
enum ParameterType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  EMAIL = 'email',
  PHONE = 'phone',
  URL = 'url'
}

// 文本分类枚举
enum TextCategory {
  PHONE = 'phone',
  ADDRESS = 'address',
  EMAIL = 'email',
  DATE = 'date',
  GENERAL = 'general'
}

// 内容替换请求（迅排设计服务接收）
interface ContentReplaceRequest {
  templateId: string
  parameterDataId: string
}

// 内容替换结果（迅排设计服务返回）
interface ContentReplaceResult {
  modifiedTemplateData: any
  previewUrl: string
}
```

##### 主项目类型定义
```typescript
// 参数配置（主项目管理）
interface ParameterConfig {
  id: string
  templateId: string
  configName: string
  configDescription?: string
  parameters: ParameterDefinition[]
  createdBy: string
  createdAt: string
  status: number
}

// 参数定义（主项目配置）
interface ParameterDefinition {
  id: string
  elementUuid: string
  parameterName: string
  parameterLabel: string
  parameterDescription?: string
  parameterType: ParameterType
  isRequired: boolean
  defaultValue?: string
  validationRules?: ValidationRule
  displayOrder: number
  isEnabled: boolean
}

// 验证规则
interface ValidationRule {
  minLength?: number
  maxLength?: number
  pattern?: string
  errorMessage?: string
}

// 用户填写数据（主项目存储）
interface UserParameterData {
  id: string
  configId: string
  templateId: string
  userId?: string
  sessionId?: string
  parameterValues: Record<string, any>
  isDraft: boolean
  previewUrl?: string
  generatedImageUrl?: string
  createdAt: string
  updatedAt: string
}
```

##### 外部API接口类型定义
```typescript
// 迅排设计调用主项目的API接口类型

// 获取参数数据响应
interface ExternalParameterDataResponse {
  code: number
  data: {
    id: string
    configId: string
    templateId: string
    parameterValues: Record<string, any>
  }
}

// 获取参数配置响应
interface ExternalParameterConfigResponse {
  code: number
  data: {
    id: string
    templateId: string
    parameters: ParameterDefinition[]
  }
}

// 健康检查响应
interface ExternalHealthResponse {
  code: number
  data: {
    status: string
    timestamp: string
  }
}
```

### 4.3 API接口设计

#### 4.3.1 迅排设计服务提供的API接口

##### 模板解析接口
```typescript
// 解析模板内容，生成参数候选项
POST /api/template/parse
{
  templateId: string
}

Response:
{
  code: 200,
  data: {
    templateId: string,
    templateTitle: string,
    textElements: TextElement[],
    parameterCandidates: ParameterCandidate[]
  }
}
```

##### 参数化预览接口
```typescript
// 生成参数化预览
POST /api/parameter/preview
{
  templateId: string,
  parameterDataId: string
}

Response:
{
  code: 200,
  data: {
    previewUrl: string,
    modifiedTemplateData: any
  }
}

// 参数化预览页面
GET /preview/parameter/:dataId
```

##### 图片生成接口
```typescript
// 参数化截图（扩展现有截图接口）
GET /api/screenshots?parameterDataId=xxx&width=xxx&height=xxx&type=file&size=xxx&quality=xxx

// 批量生成
POST /api/parameter/batch-generate
{
  dataIds: string[],
  outputOptions: {
    width: number,
    height: number,
    type: 'file' | 'cover',
    size?: number,
    quality?: number
  }
}

// 获取批量生成状态
GET /api/parameter/batch-status/:batchId
```

#### 4.3.2 主项目需要提供的外部API接口

##### 参数数据接口
```typescript
// 获取参数数据（供迅排设计调用）
GET /api/external/parameter-data/:dataId
Authorization: Bearer {apiKey}

Response:
{
  code: 200,
  data: {
    id: string,
    configId: string,
    templateId: string,
    parameterValues: Record<string, any>
  }
}
```

##### 参数配置接口
```typescript
// 获取参数配置（供迅排设计调用）
GET /api/external/parameter-config/:configId
Authorization: Bearer {apiKey}

Response:
{
  code: 200,
  data: {
    id: string,
    templateId: string,
    parameters: ParameterDefinition[]
  }
}
```

##### 健康检查接口
```typescript
// 健康检查（供迅排设计调用）
GET /api/external/health
Authorization: Bearer {apiKey}

Response:
{
  code: 200,
  data: {
    status: "ok",
    timestamp: "2025-01-16T10:00:00Z"
  }
}
```

#### 4.3.3 主项目内部API接口（主项目自己实现）

##### 参数配置管理接口
```typescript
// 创建参数配置
POST /api/parameter/config
{
  templateId: string,
  configName: string,
  configDescription?: string,
  parameters: ParameterDefinition[]
}

// 获取参数配置列表
GET /api/parameter/config/list?templateId=xxx&page=1&pageSize=10

// 获取参数配置详情
GET /api/parameter/config/:configId

// 更新参数配置
PUT /api/parameter/config/:configId

// 删除参数配置
DELETE /api/parameter/config/:configId
```

##### 用户数据管理接口
```typescript
// 提交用户填写数据
POST /api/parameter/data
{
  configId: string,
  parameterValues: Record<string, any>,
  isDraft?: boolean
}

// 获取用户填写数据
GET /api/parameter/data/:dataId

// 更新用户填写数据
PUT /api/parameter/data/:dataId

// 获取用户数据列表
GET /api/parameter/data/list?configId=xxx&userId=xxx&page=1&pageSize=10
```

## 4. 业务流程设计

### 4.1 管理员配置流程

#### 4.1.1 模板解析流程
```mermaid
graph TD
    A[选择模板] --> B[调用模板详情接口]
    B --> C[解析模板JSON数据]
    C --> D[提取layers数组]
    D --> E[识别w-text类型元素]
    E --> F[提取文本内容和样式]
    F --> G[生成参数候选项]
    G --> H[分析文本类型]
    H --> I[建议参数名称]
    I --> J[展示参数列表]
```

#### 4.1.2 参数配置流程
```mermaid
graph TD
    A[查看参数候选项] --> B[设置参数属性]
    B --> C[选择开放参数]
    C --> D[配置验证规则]
    D --> E[设置显示顺序]
    E --> F[预览表单效果]
    F --> G{配置是否满意}
    G -->|否| B
    G -->|是| H[保存参数配置]
    H --> I[生成配置ID]
```

### 4.2 用户使用流程

#### 4.2.1 表单填写流程
```mermaid
graph TD
    A[访问表单页面] --> B[加载参数配置]
    B --> C[生成动态表单]
    C --> D[用户填写表单]
    D --> E[实时表单验证]
    E --> F{验证是否通过}
    F -->|否| G[显示错误提示]
    G --> D
    F -->|是| H[保存填写数据]
    H --> I[生成预览链接]
```

#### 4.2.2 预览和生成流程
```mermaid
graph TD
    A[点击预览] --> B[加载模板数据]
    B --> C[加载用户填写数据]
    C --> D[执行内容替换]
    D --> E[渲染预览页面]
    E --> F{用户是否满意}
    F -->|否| G[返回修改表单]
    G --> A
    F -->|是| H[点击生成图片]
    H --> I[调用截图接口]
    I --> J[返回图片URL]
```

### 4.3 系统处理流程

#### 4.3.1 内容替换流程
```mermaid
graph TD
    A[获取模板数据] --> B[获取参数配置]
    B --> C[获取用户填写数据]
    C --> D[遍历参数定义]
    D --> E[定位目标元素]
    E --> F[替换文本内容]
    F --> G[保持原有样式]
    G --> H[更新元素数据]
    H --> I{是否还有参数}
    I -->|是| D
    I -->|否| J[返回替换后数据]
```

#### 4.3.2 图片生成流程
```mermaid
graph TD
    A[接收生成请求] --> B[获取参数数据]
    B --> C[执行内容替换]
    C --> D[生成预览页面URL]
    D --> E[调用Puppeteer截图]
    E --> F[图片后处理]
    F --> G[保存到文件系统]
    G --> H[返回图片URL]
```

## 5. 具体示例说明

### 5.1 基于模板ID=2的示例

#### 5.1.1 模板数据解析示例
基于提供的模板数据 `http://localhost:7001/design/temp?id=2`，系统解析结果如下：

**识别的文本元素**：
```json
[
  {
    "uuid": "98fd9b16db8a",
    "text": "你好,十二月",
    "position": {"left": 84.11, "top": 289.4, "width": 1092.38, "height": 211},
    "style": {"fontSize": 176, "color": "#000000ff", "textAlign": "center"}
  },
  {
    "uuid": "438bf33d6f9e",
    "text": "HELLONOVEMBER",
    "position": {"left": 335.609375, "top": 512.04599609375, "width": 614.58, "height": 50},
    "style": {"fontSize": 41, "color": "#000000ff", "textAlign": "center"}
  },
  {
    "uuid": "c887aa0bb7e2",
    "text": "#每日一签#",
    "position": {"left": 432.4375, "top": 1522.7162109375, "width": 398.65, "height": 80},
    "style": {"fontSize": 66, "color": "#000000ff", "textAlign": "center"}
  },
  {
    "uuid": "b12ffac161d3",
    "text": "生活就像海洋，只有意志坚强的人，<br/>才能到达彼岸。——马克思",
    "position": {"left": 219.45423141017832, "top": 1646.7887675261356, "width": 806.5, "height": 147},
    "style": {"fontSize": 45, "color": "#000000ff", "textAlign": "center"}
  },
  {
    "uuid": "764a25774e74",
    "text": "电话：8888-8888888<br/>地址：广州市高林路888号",
    "position": {"left": 160.2270573384873, "top": 1924.3892888940566, "width": 600.53, "height": 131},
    "style": {"fontSize": 45, "color": "#000000ff", "textAlign": "left"}
  }
]
```

**生成的参数候选项**：
```json
[
  {
    "elementUuid": "98fd9b16db8a",
    "suggestedName": "greeting_text",
    "suggestedLabel": "问候语",
    "suggestedType": "text",
    "originalText": "你好,十二月",
    "textCategory": "general"
  },
  {
    "elementUuid": "438bf33d6f9e",
    "suggestedName": "subtitle_text",
    "suggestedLabel": "副标题",
    "suggestedType": "text",
    "originalText": "HELLONOVEMBER",
    "textCategory": "general"
  },
  {
    "elementUuid": "c887aa0bb7e2",
    "suggestedName": "tag_text",
    "suggestedLabel": "标签文字",
    "suggestedType": "text",
    "originalText": "#每日一签#",
    "textCategory": "general"
  },
  {
    "elementUuid": "b12ffac161d3",
    "suggestedName": "quote_text",
    "suggestedLabel": "名言内容",
    "suggestedType": "textarea",
    "originalText": "生活就像海洋，只有意志坚强的人，<br/>才能到达彼岸。——马克思",
    "textCategory": "general"
  },
  {
    "elementUuid": "764a25774e74",
    "suggestedName": "contact_info",
    "suggestedLabel": "联系信息",
    "suggestedType": "textarea",
    "originalText": "电话：8888-8888888<br/>地址：广州市高林路888号",
    "textCategory": "contact"
  }
]
```

#### 5.1.2 管理员配置示例
管理员选择开放以下参数供用户填写：

**选择的参数配置**：
```json
{
  "configName": "个性化日签配置",
  "configDescription": "用户可自定义问候语、名言和联系信息",
  "parameters": [
    {
      "elementUuid": "98fd9b16db8a",
      "parameterName": "greeting",
      "parameterLabel": "个性问候语",
      "parameterDescription": "请输入您的个性化问候语",
      "parameterType": "text",
      "isRequired": true,
      "defaultValue": "你好,十二月",
      "validationRules": {"maxLength": 20},
      "displayOrder": 1,
      "isEnabled": true
    },
    {
      "elementUuid": "b12ffac161d3",
      "parameterName": "quote",
      "parameterLabel": "励志名言",
      "parameterDescription": "请输入您喜欢的励志名言",
      "parameterType": "textarea",
      "isRequired": true,
      "defaultValue": "生活就像海洋，只有意志坚强的人，才能到达彼岸。——马克思",
      "validationRules": {"maxLength": 100},
      "displayOrder": 2,
      "isEnabled": true
    },
    {
      "elementUuid": "764a25774e74",
      "parameterName": "contact",
      "parameterLabel": "联系方式",
      "parameterDescription": "请输入您的联系电话和地址",
      "parameterType": "textarea",
      "isRequired": false,
      "defaultValue": "电话：8888-8888888\n地址：广州市高林路888号",
      "validationRules": {"maxLength": 200},
      "displayOrder": 3,
      "isEnabled": true
    }
  ]
}
```

#### 5.1.3 用户填写示例
用户访问表单页面，看到以下表单：

**生成的用户表单**：
```html
<form>
  <div class="form-field">
    <label>个性问候语 *</label>
    <input type="text" name="greeting" value="你好,十二月" maxlength="20" required>
    <small>请输入您的个性化问候语</small>
  </div>

  <div class="form-field">
    <label>励志名言 *</label>
    <textarea name="quote" maxlength="100" required>生活就像海洋，只有意志坚强的人，才能到达彼岸。——马克思</textarea>
    <small>请输入您喜欢的励志名言</small>
  </div>

  <div class="form-field">
    <label>联系方式</label>
    <textarea name="contact" maxlength="200">电话：8888-8888888
地址：广州市高林路888号</textarea>
    <small>请输入您的联系电话和地址</small>
  </div>

  <button type="submit">预览效果</button>
  <button type="button">生成图片</button>
</form>
```

**用户填写的数据**：
```json
{
  "greeting": "你好,新年快乐",
  "quote": "成功不是终点，失败不是致命的，重要的是继续前进的勇气。——丘吉尔",
  "contact": "电话：138-0000-0000\n地址：北京市朝阳区xxx路123号"
}
```

#### 5.1.4 内容替换示例
系统执行内容替换后的模板数据：

**替换后的文本元素**：
- `uuid: "98fd9b16db8a"` 的 `text` 从 `"你好,十二月"` 替换为 `"你好,新年快乐"`
- `uuid: "b12ffac161d3"` 的 `text` 从 `"生活就像海洋，只有意志坚强的人，<br/>才能到达彼岸。——马克思"` 替换为 `"成功不是终点，失败不是致命的，<br/>重要的是继续前进的勇气。——丘吉尔"`
- `uuid: "764a25774e74"` 的 `text` 从 `"电话：8888-8888888<br/>地址：广州市高林路888号"` 替换为 `"电话：138-0000-0000<br/>地址：北京市朝阳区xxx路123号"`

**保持的样式属性**：
- 字体大小、颜色、对齐方式等样式属性保持不变
- 元素位置和尺寸保持不变
- 富文本格式（如 `<br/>` 标签）得到保留

## 6. 技术要求和约束

### 6.1 技术要求

#### 6.1.1 富文本内容支持
- **HTML标签保留**：支持 `<br/>`、`<span>`、`<strong>` 等基础HTML标签
- **样式保持**：参数替换后保持原有的字体、颜色、大小等样式属性
- **格式化处理**：支持文本的自动换行和格式化
- **特殊字符处理**：正确处理特殊字符和转义字符

#### 6.1.2 参数验证机制
- **前端验证**：实时验证用户输入，提供即时反馈
- **后端验证**：服务端二次验证，确保数据安全性
- **格式校验**：支持电话号码、邮箱、URL等格式验证
- **长度限制**：支持最小/最大长度限制
- **自定义规则**：支持正则表达式自定义验证规则

#### 6.1.3 参数预设默认值
- **智能默认值**：根据文本内容类型自动设置合理默认值
- **用户自定义**：管理员可为每个参数设置自定义默认值
- **动态默认值**：支持基于当前时间、用户信息等的动态默认值
- **默认值验证**：确保默认值符合验证规则

#### 6.1.4 多语言支持扩展性
- **国际化框架**：使用 Vue I18n 支持多语言
- **参数标签多语言**：参数标签和描述支持多语言配置
- **错误信息多语言**：验证错误信息支持多语言显示
- **模板内容多语言**：为未来模板内容多语言化预留扩展接口

### 6.2 性能要求

#### 6.2.1 响应时间要求
- **模板解析**：< 1秒（解析模板JSON并生成参数候选项）
- **表单生成**：< 0.5秒（根据配置生成用户表单）
- **实时预览**：< 2秒（参数替换并渲染预览）
- **图片生成**：< 5秒（调用截图服务生成最终图片）
- **批量处理**：单个任务 < 10秒，支持并发处理

#### 6.2.2 并发处理能力
- **用户表单访问**：支持1000个并发用户同时访问表单
- **预览渲染**：支持100个并发预览请求
- **图片生成**：支持50个并发图片生成请求
- **数据库连接**：合理配置连接池，支持高并发访问

#### 6.2.3 存储和缓存
- **参数配置缓存**：热门配置缓存到Redis，提高访问速度
- **模板数据缓存**：模板数据缓存，减少重复解析
- **生成结果缓存**：相同参数的生成结果缓存，避免重复计算
- **文件存储优化**：生成的图片文件合理分目录存储

### 6.3 安全要求

#### 6.3.1 数据安全
- **输入过滤**：过滤用户输入中的恶意脚本和SQL注入
- **数据加密**：敏感数据传输使用HTTPS加密
- **访问控制**：基于角色的权限管理，管理员和用户权限分离
- **数据备份**：定期备份参数配置和用户数据

#### 6.3.2 内容安全
- **内容审核**：用户填写内容进行基础的敏感词过滤
- **文件安全**：生成的图片文件安全存储，防止恶意访问
- **接口限流**：API接口实现限流机制，防止恶意调用
- **日志记录**：记录关键操作日志，便于安全审计

### 6.4 兼容性要求

#### 6.4.1 浏览器兼容性
- **现代浏览器**：支持Chrome 90+、Firefox 88+、Safari 14+、Edge 90+
- **移动端浏览器**：支持移动端Chrome、Safari浏览器
- **响应式设计**：表单和预览页面支持响应式布局
- **渐进增强**：核心功能在低版本浏览器中降级可用

#### 6.4.2 系统兼容性
- **操作系统**：支持Windows、Linux、macOS部署
- **Node.js版本**：支持Node.js 16+版本
- **数据库**：支持MySQL 8.0+、PostgreSQL 12+
- **容器化**：支持Docker容器化部署

## 7. 实现计划

### 7.1 开发阶段规划（API网关模式）

#### 7.1.1 第一阶段：迅排设计服务 - 模板解析引擎（2周）
**目标**：在迅排设计服务中实现模板内容解析和参数候选项生成

**主要任务**：
- 开发模板JSON数据解析功能
- 实现文本元素识别和提取算法
- 开发参数候选项生成算法
- 实现文本内容分类和参数名称建议
- 创建模板解析API接口 `POST /api/template/parse`

**交付物**：
- 模板解析引擎核心代码
- 参数候选项生成API
- 单元测试用例
- API文档

#### 7.1.2 第二阶段：迅排设计服务 - 外部API集成（2周）
**目标**：实现与主项目的API集成功能

**主要任务**：
- 开发外部API调用服务
- 实现API重试和缓存机制
- 开发参数数据获取功能
- 实现连接健康检查
- 添加错误处理和日志记录

**交付物**：
- 外部API集成模块
- 缓存和重试机制
- 健康检查功能
- 集成测试用例

#### 7.1.3 第三阶段：迅排设计服务 - 参数替换和预览（2周）
**目标**：实现参数内容替换和预览功能

**主要任务**：
- 开发参数内容替换引擎
- 实现富文本内容处理
- 扩展现有预览组件支持参数化
- 开发预览页面 `GET /preview/parameter/:dataId`
- 创建预览生成API `POST /api/parameter/preview`

**交付物**：
- 内容替换引擎
- 参数化预览页面
- 预览API接口
- 前端预览组件

#### 7.1.4 第四阶段：迅排设计服务 - 图片生成扩展（1周）
**目标**：扩展现有截图服务支持参数化

**主要任务**：
- 扩展现有截图接口支持 `parameterDataId` 参数
- 实现批量生成功能
- 基于现有队列系统实现批量处理
- 优化图片生成性能

**交付物**：
- 参数化截图功能
- 批量生成API
- 队列处理优化
- 性能测试报告

#### 7.1.5 第五阶段：主项目集成指导（1周）
**目标**：输出主项目集成文档和示例代码

**主要任务**：
- 编写主项目集成指南
- 提供API实现示例代码
- 编写数据库设计文档
- 提供前端界面开发指导
- 编写部署和配置文档

**交付物**：
- 主项目集成指南
- API实现示例代码
- 数据库设计文档
- 前端开发指导
- 部署配置文档

### 7.2 里程碑计划

#### 7.2.1 M1：迅排设计服务 - 模板解析完成（第2周末）
- ✅ 模板JSON数据解析功能
- ✅ 文本元素识别和参数候选项生成
- ✅ 模板解析API接口实现
- ✅ 核心算法单元测试

#### 7.2.2 M2：迅排设计服务 - 外部API集成完成（第4周末）
- ✅ 外部API调用服务实现
- ✅ API重试和缓存机制
- ✅ 健康检查功能
- ✅ 集成测试完成

#### 7.2.3 M3：迅排设计服务 - 参数替换和预览完成（第6周末）
- ✅ 参数内容替换引擎
- ✅ 参数化预览功能
- ✅ 预览API接口实现
- ✅ 前端预览组件

#### 7.2.4 M4：迅排设计服务 - 图片生成扩展完成（第7周末）
- ✅ 参数化截图功能
- ✅ 批量生成API实现
- ✅ 队列处理优化
- ✅ 性能测试通过

#### 7.2.5 M5：主项目集成文档完成（第8周末）
- ✅ 主项目集成指南
- ✅ API实现示例代码
- ✅ 数据库设计文档
- ✅ 部署配置文档

### 7.3 资源需求

#### 7.3.1 人力资源（迅排设计服务开发）
- **后端开发工程师**：1人，负责迅排设计服务API接口和核心引擎开发
- **前端开发工程师**：0.5人，负责预览页面组件开发
- **测试工程师**：0.5人，负责功能测试和集成测试
- **技术文档工程师**：0.5人，负责集成文档和示例代码编写

#### 7.3.2 技术资源（迅排设计服务）
- **开发环境**：基于现有迅排设计项目环境
- **测试环境**：独立的测试环境用于功能验证
- **数据存储**：继续使用现有文件系统，无需数据库
- **服务器资源**：复用现有服务器资源
- **第三方服务**：无需额外第三方服务

#### 7.3.3 主项目集成资源（由主项目团队负责）
- **后端开发工程师**：1人，负责主项目API接口和数据库设计
- **前端开发工程师**：1人，负责管理员界面和用户表单开发
- **数据库管理员**：0.5人，负责数据库设计和优化
- **测试工程师**：0.5人，负责主项目功能测试

### 7.4 风险评估和应对

#### 7.4.1 技术风险
**风险**：富文本内容替换的复杂性
**影响**：可能导致样式丢失或格式错乱
**应对**：提前进行技术验证，制定详细的文本处理规范

**风险**：大量并发请求的性能问题
**影响**：可能导致系统响应缓慢
**应对**：实现缓存机制，优化数据库查询，使用队列处理

#### 7.4.2 进度风险
**风险**：需求变更导致开发延期
**影响**：可能影响整体上线时间
**应对**：严格控制需求变更，优先实现核心功能

**风险**：与现有系统集成的兼容性问题
**影响**：可能需要额外的适配工作
**应对**：提前进行兼容性测试，预留缓冲时间

#### 7.4.3 质量风险
**风险**：用户输入数据的安全性问题
**影响**：可能存在安全漏洞
**应对**：实施严格的输入验证和过滤机制

**风险**：生成图片的质量问题
**影响**：可能影响用户体验
**应对**：充分测试各种参数组合，建立质量检查机制

## 8. 总结

### 8.1 项目价值总结

#### 8.1.1 业务价值
- **服务化能力**：将迅排设计的核心能力服务化，可被其他系统集成
- **降低集成门槛**：主项目无需设计技能即可提供参数化设计服务
- **提高模板复用率**：一个模板可支持多种个性化配置方案
- **扩展商业模式**：为SaaS服务和API服务提供技术基础
- **增强竞争优势**：智能参数识别功能具有技术领先性

#### 8.1.2 技术价值
- **智能内容识别**：自动解析模板内容，减少人工配置工作
- **API网关架构**：清晰的职责分离，便于维护和扩展
- **高质量内容替换**：保持原有样式的同时实现内容个性化
- **服务复用性**：迅排设计服务可被多个主项目同时使用
- **技术标准化**：建立了参数化模板的技术标准和规范

### 8.2 实现可行性分析

#### 8.2.1 技术可行性
- **基础设施完善**：现有迅排设计项目提供了完整的技术基础
- **核心功能复用**：截图服务、文件管理等功能可直接复用
- **技术栈一致**：使用相同的技术栈，团队学习成本低
- **架构清晰**：API网关模式职责分离明确，便于开发和维护

#### 8.2.2 资源可行性
- **开发资源合理**：8周开发周期，2.5人团队配置适中
- **技术难度适中**：主要为业务逻辑开发，无高难度技术挑战
- **测试资源到位**：配备测试工程师，保证代码质量
- **部署环境现成**：复用现有部署环境，无额外基础设施成本

#### 8.2.3 商业可行性
- **市场需求明确**：API服务化是当前技术发展趋势
- **竞争优势明显**：智能参数识别功能具有技术创新性
- **成本控制合理**：基于现有项目扩展，开发成本可控
- **商业模式清晰**：可为多个主项目提供服务，具有良好的商业前景

### 8.3 成功关键因素

#### 8.3.1 技术关键因素
- **模板解析准确性**：确保文本元素识别的准确率
- **内容替换质量**：保证替换后的样式和格式正确性
- **系统性能优化**：确保在高并发下的稳定性
- **用户体验优化**：简化操作流程，提升易用性

#### 8.3.2 产品关键因素
- **参数配置灵活性**：满足不同模板的配置需求
- **表单生成智能化**：根据参数类型生成合适的表单控件
- **预览效果实时性**：提供即时的预览反馈
- **错误处理友好性**：提供清晰的错误提示和处理建议

#### 8.3.3 运营关键因素
- **用户教育培训**：帮助用户快速掌握新功能
- **模板质量控制**：确保提供高质量的参数化模板
- **反馈收集机制**：及时收集用户反馈，持续优化
- **技术支持服务**：提供及时的技术支持和问题解决

### 8.4 后续发展规划

#### 8.4.1 功能扩展规划
- **图片参数化**：支持图片元素的参数化替换
- **样式参数化**：支持颜色、字体等样式属性的参数化
- **条件逻辑**：支持基于参数值的条件显示逻辑
- **模板推荐**：基于用户行为推荐合适的参数化模板

#### 8.4.2 技术优化规划
- **AI智能识别**：使用AI技术提升参数识别的智能化水平
- **性能优化**：持续优化系统性能，支持更大规模的并发
- **多端适配**：扩展到移动端，提供更好的移动体验
- **API开放**：提供开放API，支持第三方系统集成

#### 8.4.3 商业化规划
- **企业版功能**：为企业用户提供更高级的定制功能
- **模板市场**：建立参数化模板交易市场
- **SaaS服务**：提供独立的参数化设计SaaS服务
- **行业解决方案**：针对特定行业提供专业解决方案

## 9. 快速开始指南

### 9.1 文档阅读顺序
如果您是第一次接触这个项目，建议按以下顺序阅读文档：

1. **首先阅读本需求文档**：
   - 理解项目背景和核心价值（第1章）
   - 了解功能需求和架构设计（第2-4章）
   - 查看具体示例说明（第5章）
   - 了解实现计划（第7章）

2. **然后阅读技术实现文档**：
   - 理解技术架构和实现方案
   - 查看详细的代码实现指导
   - 了解API接口设计
   - 学习部署和测试方法

### 9.2 立即开始开发
如果您需要立即开始开发，请按以下步骤进行：

#### 步骤1：环境准备
```bash
# 确保您在迅排设计项目根目录
cd f:\VMwareFile\poster-design

# 检查当前项目状态
npm run serve  # 确保现有项目正常运行
```

#### 步骤2：创建开发分支
```bash
git checkout -b feature/dynamic-parameter-system
```

#### 步骤3：开始第一阶段开发
按照《动态参数模板系统技术实现文档》第4章的指导，开始模板解析引擎的开发：

1. 创建项目结构：`service/src/services/templateParser.ts`
2. 实现模板解析服务
3. 创建API接口：`POST /api/template/parse`
4. 编写单元测试

#### 步骤4：参考示例
使用第5章中基于模板ID=2的具体示例来验证您的实现。

### 9.3 关键文件位置
- **需求文档**：`docs/动态参数模板系统需求文档.md`
- **技术实现文档**：`docs/动态参数模板系统技术实现文档.md`
- **示例模板数据**：`service/src/mock/templates/2.json`
- **现有模板接口**：`service/src/service/design.ts`

### 9.4 重要提醒
- 本项目采用**API网关模式**，迅排设计只作为服务提供方
- 不需要在迅排设计中实现数据库，继续使用文件系统
- 重点关注模板解析、参数替换、图片生成等核心功能
- 最终目标是输出完整的集成文档供主项目使用

---

**文档版本**: v2.1
**创建日期**: 2025-01-16
**最后更新**: 2025-01-16
**文档作者**: AI Assistant
**审核状态**: 待审核
