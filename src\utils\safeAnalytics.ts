/**
 * 安全的统计脚本加载器
 * 避免权限策略冲突，不使用unload事件
 */

interface AnalyticsConfig {
  enableBaidu?: boolean
  enableBusuanzi?: boolean
  baiduId?: string
}

class SafeAnalytics {
  private config: AnalyticsConfig
  private loaded: Set<string> = new Set()

  constructor(config: AnalyticsConfig = {}) {
    this.config = {
      enableBaidu: true,
      enableBusuanzi: false, // 默认禁用，因为可能使用unload事件
      baiduId: '21238d2872af8b12083429237026b84c',
      ...config
    }
  }

  /**
   * 初始化统计
   */
  init() {
    if (this.config.enableBaidu) {
      this.loadBaiduAnalytics()
    }

    if (this.config.enableBusuanzi) {
      this.loadBusuanziAnalytics()
    }
  }

  /**
   * 安全加载百度统计
   */
  private loadBaiduAnalytics() {
    if (this.loaded.has('baidu')) return

    try {
      // 初始化百度统计全局变量
      if (!(window as any)._hmt) {
        (window as any)._hmt = []
      }

      const script = document.createElement('script')
      script.src = `https://hm.baidu.com/hm.js?${this.config.baiduId}`
      script.async = true
      script.defer = true

      script.onload = () => {
        // 只在开发环境显示加载信息
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 百度统计加载成功')
        }
        this.loaded.add('baidu')

        // 加载完成后立即设置页面离开事件监听
        this.setupPageLeaveTracking()
      }

      script.onerror = () => {
        console.warn('百度统计加载失败')
      }

      // 使用更安全的方式插入脚本
      const firstScript = document.getElementsByTagName('script')[0]
      if (firstScript && firstScript.parentNode) {
        firstScript.parentNode.insertBefore(script, firstScript)
      } else {
        document.head.appendChild(script)
      }
    } catch (error) {
      console.warn('百度统计初始化失败:', error)
    }
  }

  /**
   * 设置页面离开追踪（替代unload事件）
   */
  private setupPageLeaveTracking() {
    // 使用 visibilitychange 事件替代 unload
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.trackPageLeave()
      }
    })

    // 使用 beforeunload 作为备用
    window.addEventListener('beforeunload', () => {
      this.trackPageLeave()
    })

    // 使用 pagehide 事件（更可靠）
    window.addEventListener('pagehide', () => {
      this.trackPageLeave()
    })
  }

  /**
   * 页面离开时的统计
   */
  private trackPageLeave() {
    try {
      if ((window as any)._hmt) {
        // 发送页面停留时间统计
        (window as any)._hmt.push(['_trackEvent', 'page', 'leave', location.pathname])
      }
    } catch (error) {
      console.warn('页面离开统计失败:', error)
    }
  }

  /**
   * 安全加载不蒜子统计（如果需要的话）
   */
  private loadBusuanziAnalytics() {
    if (this.loaded.has('busuanzi')) return

    try {
      const script = document.createElement('script')
      script.src = '//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js'
      script.async = true
      
      script.onload = () => {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ 不蒜子统计加载成功')
        }
        this.loaded.add('busuanzi')
      }
      
      script.onerror = () => {
        console.warn('不蒜子统计加载失败')
      }

      document.head.appendChild(script)
    } catch (error) {
      console.warn('不蒜子统计初始化失败:', error)
    }
  }

  /**
   * 手动发送页面浏览事件（替代unload事件）
   */
  trackPageView(page?: string) {
    try {
      if ((window as any)._hmt) {
        (window as any)._hmt.push(['_trackPageview', page || location.pathname])
      }
    } catch (error) {
      console.warn('页面浏览统计失败:', error)
    }
  }

  /**
   * 手动发送事件统计
   */
  trackEvent(category: string, action: string, label?: string, value?: number) {
    try {
      if ((window as any)._hmt) {
        (window as any)._hmt.push(['_trackEvent', category, action, label, value])
      }
    } catch (error) {
      console.warn('事件统计失败:', error)
    }
  }

  /**
   * 清理统计脚本（组件卸载时调用）
   */
  cleanup() {
    // 不需要特殊清理，因为我们没有使用unload事件
    console.log('统计脚本清理完成')
  }
}

// 创建全局实例
const safeAnalytics = new SafeAnalytics()

export default safeAnalytics
