# 动态参数模板系统 - 主项目集成指南

## 概述

本文档详细说明如何在主项目中集成动态参数模板系统，实现与迅排设计的API网关模式集成。

## 架构说明

### 系统架构
```
┌─────────────────┐    API调用    ┌─────────────────┐
│   主项目        │ ──────────→  │   迅排设计      │
│ (数据管理)      │              │ (服务提供)      │
│                 │ ←────────────│                 │
└─────────────────┘    响应数据    └─────────────────┘
```

### 职责分工
- **主项目**: 负责参数数据管理、用户界面、业务逻辑
- **迅排设计**: 负责模板解析、内容替换、图片生成

## 集成步骤

### 1. 环境准备

#### 1.1 启动迅排设计服务
```bash
# 克隆迅排设计项目
git clone https://github.com/palxiao/poster-design.git
cd poster-design

# 安装依赖
npm run prepared

# 启动服务 (默认端口7001)
npm run serve
```

#### 1.2 配置API Key
在迅排设计的 `service/.env` 文件中配置：
```env
EXTERNAL_API_KEY=your-secure-api-key
```

### 2. 主项目API实现

#### 2.1 必需的API接口

主项目需要实现以下API接口供迅排设计调用：

**获取参数数据**
```
GET /api/external/parameter-data/:dataId
Authorization: Bearer your-secure-api-key
```

**获取参数配置**
```
GET /api/external/parameter-config/:configId
Authorization: Bearer your-secure-api-key
```

**健康检查**
```
GET /api/external/health
Authorization: Bearer your-secure-api-key
```

#### 2.2 API响应格式

所有API响应统一使用以下格式：
```json
{
  "code": 200,
  "data": {
    // 具体数据
  }
}
```

### 3. 数据库设计

#### 3.1 参数配置表 (parameter_configs)
```sql
CREATE TABLE parameter_configs (
  id VARCHAR(50) PRIMARY KEY,
  template_id VARCHAR(50) NOT NULL,
  parameters JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3.2 参数数据表 (parameter_data)
```sql
CREATE TABLE parameter_data (
  id VARCHAR(50) PRIMARY KEY,
  config_id VARCHAR(50) NOT NULL,
  template_id VARCHAR(50) NOT NULL,
  parameter_values JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (config_id) REFERENCES parameter_configs(id)
);
```

### 4. 使用流程

#### 4.1 模板解析
```javascript
// 调用迅排设计API解析模板
const response = await fetch('http://localhost:7001/api/template/parse', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ templateId: '2' })
});

const result = await response.json();
// result.data.parameterCandidates 包含参数候选项
```

#### 4.2 参数配置
```javascript
// 管理员配置参数
const config = {
  id: 'config-456',
  templateId: '2',
  parameters: [
    {
      id: 'param-1',
      elementUuid: '98fd9b16db8a',
      parameterName: 'greeting',
      parameterLabel: '问候语',
      parameterType: 'text',
      isRequired: true,
      isEnabled: true,
      displayOrder: 1
    }
  ]
};

// 保存到数据库
await saveParameterConfig(config);
```

#### 4.3 用户填写参数
```javascript
// 用户填写参数值
const parameterData = {
  id: 'user-data-123',
  configId: 'config-456',
  templateId: '2',
  parameterValues: {
    greeting: '你好,新年快乐',
    quote: '成功不是终点...'
  }
};

// 保存到数据库
await saveParameterData(parameterData);
```

#### 4.4 生成预览
```javascript
// 调用迅排设计API生成预览
const response = await fetch('http://localhost:7001/api/parameter/preview', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    templateId: '2',
    parameterDataId: 'user-data-123'
  })
});

const result = await response.json();
// result.data.previewUrl 为预览页面URL
```

#### 4.5 生成图片
```javascript
// 单个图片生成
const imageUrl = `http://localhost:7001/api/screenshots?parameterDataId=user-data-123&width=800&height=600`;

// 批量图片生成
const batchResponse = await fetch('http://localhost:7001/api/parameter/batch-generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    templateId: '2',
    parameterDataIds: ['user-data-123', 'user-data-124'],
    options: { width: 800, height: 600 }
  })
});
```

## 注意事项

### 1. 安全性
- 使用强密码作为API Key
- 在生产环境中使用HTTPS
- 定期轮换API Key

### 2. 性能优化
- 实现API响应缓存
- 使用数据库连接池
- 批量操作时控制并发数量

### 3. 错误处理
- 实现完善的错误处理机制
- 记录详细的错误日志
- 提供用户友好的错误提示

### 4. 监控和日志
- 监控API调用频率和响应时间
- 记录关键操作日志
- 设置告警机制

## 示例代码

完整的示例代码请参考 `examples/` 目录中的示例项目。

## 技术支持

如有问题，请参考：
- [API接口文档](./API接口文档.md)
- [故障排查指南](./故障排查指南.md)
- [性能优化建议](./性能优化建议.md)
