// 参数相关类型定义
export enum ParameterType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  EMAIL = 'email',
  PHONE = 'phone',
  URL = 'url'
}

export enum TextCategory {
  PHONE = 'phone',
  ADDRESS = 'address',
  EMAIL = 'email',
  DATE = 'date',
  GENERAL = 'general'
}

export interface ElementPosition {
  left: number
  top: number
  width: number
  height: number
}

export interface ElementStyle {
  fontSize: number
  color: string
  fontFamily: string
  textAlign: string
  lineHeight: number
  letterSpacing: number
  fontWeight: string | number
  fontStyle: string
}

export interface TextElement {
  uuid: string
  type: 'w-text'
  text: string
  position: ElementPosition
  style: ElementStyle
}

export interface ParameterCandidate {
  elementUuid: string
  suggestedName: string
  suggestedLabel: string
  suggestedType: ParameterType
  originalText: string
  textCategory: TextCategory
}

export interface TemplateParseResult {
  templateId: string
  templateTitle: string
  textElements: TextElement[]
  parameterCandidates: ParameterCandidate[]
}

export interface ParameterDefinition {
  id: string
  elementUuid: string
  parameterName: string
  parameterLabel: string
  parameterType: ParameterType
  isRequired: boolean
  defaultValue?: string
  validationRules?: ValidationRule
  displayOrder: number
  isEnabled: boolean
}

export interface ValidationRule {
  minLength?: number
  maxLength?: number
  pattern?: string
  errorMessage?: string
}

export interface ContentReplaceRequest {
  templateId: string
  parameterDataId: string
}

export interface ContentReplaceResult {
  modifiedTemplateData: any
  previewUrl: string
}

export interface ParameterData {
  id: string
  configId: string
  parameterValues: Record<string, any>
  templateId: string
}

export interface ParameterConfig {
  id: string
  templateId: string
  parameters: ParameterDefinition[]
}

export interface ExternalApiConfig {
  baseUrl: string
  apiKey: string
  timeout: number
  retryAttempts: number
}

export interface ExternalParameterDataResponse {
  code: number
  data: {
    id: string
    configId: string
    templateId: string
    parameterValues: Record<string, any>
  }
}

export interface ExternalParameterConfigResponse {
  code: number
  data: {
    id: string
    templateId: string
    parameters: ParameterDefinition[]
  }
}

export interface ExternalHealthResponse {
  code: number
  data: {
    status: string
    timestamp: string
  }
}
