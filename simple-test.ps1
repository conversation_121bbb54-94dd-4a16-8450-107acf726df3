# 简化的API测试脚本

Write-Host "=== 动态参数模板系统API测试 ===" -ForegroundColor Green

# 测试计数器
$totalTests = 0
$passedTests = 0

function Test-Api {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = "",
        [int]$ExpectedStatus = 200
    )
    
    $script:totalTests++
    Write-Host "测试: $Name" -ForegroundColor Yellow
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
        }
        
        if ($Body -and $Method -eq "POST") {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        
        if ($response.StatusCode -eq $ExpectedStatus) {
            Write-Host "  ✓ PASS - Status: $($response.StatusCode)" -ForegroundColor Green
            $script:passedTests++
        } else {
            Write-Host "  ✗ FAIL - Expected: $ExpectedStatus, Got: $($response.StatusCode)" -ForegroundColor Red
        }
        
        # 显示响应内容的前100个字符
        $content = $response.Content
        if ($content.Length -gt 100) {
            $content = $content.Substring(0, 100) + "..."
        }
        Write-Host "  Response: $content" -ForegroundColor Gray
        
    } catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        if ($statusCode -eq $ExpectedStatus) {
            Write-Host "  ✓ PASS - Expected error status: $statusCode" -ForegroundColor Green
            $script:passedTests++
        } else {
            Write-Host "  ✗ FAIL - Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host ""
}

Write-Host "1. 测试模拟API服务器" -ForegroundColor Cyan
Write-Host "================================"

# 健康检查
Test-Api -Name "健康检查-正常" -Url "http://localhost:3000/api/external/health" -Headers @{"Authorization"="Bearer test-api-key"}

# 无效API Key
Test-Api -Name "健康检查-无效Key" -Url "http://localhost:3000/api/external/health" -Headers @{"Authorization"="Bearer invalid"} -ExpectedStatus 401

# 获取参数数据
Test-Api -Name "获取参数数据-正常" -Url "http://localhost:3000/api/external/parameter-data/user-data-123" -Headers @{"Authorization"="Bearer test-api-key"}

# 获取不存在的参数数据
Test-Api -Name "获取参数数据-不存在" -Url "http://localhost:3000/api/external/parameter-data/invalid" -Headers @{"Authorization"="Bearer test-api-key"} -ExpectedStatus 404

# 获取参数配置
Test-Api -Name "获取参数配置-正常" -Url "http://localhost:3000/api/external/parameter-config/config-456" -Headers @{"Authorization"="Bearer test-api-key"}

Write-Host "2. 测试迅排设计API" -ForegroundColor Cyan
Write-Host "================================"

# 模板解析
Test-Api -Name "模板解析-正常" -Url "http://localhost:7001/api/template/parse" -Method "POST" -Body '{"templateId": "2"}'

# 模板解析-缺失参数
Test-Api -Name "模板解析-缺失参数" -Url "http://localhost:7001/api/template/parse" -Method "POST" -Body '{}' -ExpectedStatus 400

# 参数化预览
Test-Api -Name "参数化预览-正常" -Url "http://localhost:7001/api/parameter/preview" -Method "POST" -Body '{"templateId": "2", "parameterDataId": "user-data-123"}'

# 预览页面
Test-Api -Name "预览页面-正常" -Url "http://localhost:7001/preview/parameter/user-data-123"

Write-Host "3. 测试结果汇总" -ForegroundColor Cyan
Write-Host "================================"

$failedTests = $totalTests - $passedTests
$passRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }

Write-Host "总测试数: $totalTests" -ForegroundColor White
Write-Host "通过数: $passedTests" -ForegroundColor Green
Write-Host "失败数: $failedTests" -ForegroundColor Red
Write-Host "通过率: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } else { "Red" })

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
