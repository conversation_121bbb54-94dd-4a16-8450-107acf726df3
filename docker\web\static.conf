location / {
	try_files $uri $uri/ /index.html;
	add_header Cache-Control no-cache;
	proxy_set_header Host $host;
	proxy_set_header X-Real-IP $remote_addr;
	proxy_set_header X-Forward-For $proxy_add_x_forwarded_for;
}

location ^~/design/ {
	proxy_pass http://127.0.0.1:7001;
	proxy_set_header Host $host;
	proxy_set_header X-Real-IP $remote_addr;
	proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}