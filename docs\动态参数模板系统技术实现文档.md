# 动态参数模板系统技术实现文档

## 1. 文档概述

### 1.1 文档目的
本文档详细描述了在迅排设计项目中实现动态参数模板系统的技术方案，以及如何与外部主项目进行API集成。

### 1.2 架构模式
采用API网关模式，迅排设计作为服务提供方，主项目负责数据管理和用户界面。

```
主项目 ←→ API网关 ←→ 迅排设计服务
   ↓                      ↓
主项目数据库           文件系统/缓存
```

### 1.3 职责分工
- **迅排设计服务**：模板解析、内容替换、图片生成
- **主项目**：参数配置管理、用户数据存储、前端界面

## 2. 迅排设计服务实现

### 2.1 项目结构扩展

```
service/src/
├── configs.ts                 # 配置文件（扩展）
├── services/                  # 新增服务层
│   ├── templateParser.ts      # 模板解析服务
│   ├── parameterEngine.ts     # 参数替换引擎
│   └── externalApi.ts         # 外部API调用服务
├── controllers/               # 新增控制器
│   ├── parameterController.ts # 参数化相关控制器
│   └── templateController.ts  # 模板相关控制器
├── types/                     # 新增类型定义
│   └── parameter.d.ts         # 参数相关类型
├── utils/                     # 工具函数（扩展）
│   └── contentReplacer.ts     # 内容替换工具
└── routes/                    # 路由（扩展）
    └── parameter.ts           # 参数化路由
```

### 2.2 核心服务实现

#### 2.2.1 模板解析服务
```typescript
// service/src/services/templateParser.ts
export interface TemplateParseResult {
  templateId: string
  templateTitle: string
  textElements: TextElement[]
  parameterCandidates: ParameterCandidate[]
}

export interface TextElement {
  uuid: string
  type: 'w-text'
  text: string
  position: ElementPosition
  style: ElementStyle
}

export interface ParameterCandidate {
  elementUuid: string
  suggestedName: string
  suggestedLabel: string
  suggestedType: ParameterType
  originalText: string
  textCategory: TextCategory
}

export class TemplateParserService {
  async parseTemplate(templateId: string): Promise<TemplateParseResult>
  private extractTextElements(templateData: any): TextElement[]
  private generateParameterCandidates(textElements: TextElement[]): ParameterCandidate[]
  private analyzeTextCategory(text: string): TextCategory
  private suggestParameterName(text: string, category: TextCategory): string
}
```

#### 2.2.2 参数替换引擎
```typescript
// service/src/services/parameterEngine.ts
export interface ContentReplaceRequest {
  templateId: string
  parameterDataId: string
}

export interface ContentReplaceResult {
  modifiedTemplateData: any
  previewUrl: string
}

export class ParameterEngineService {
  async replaceContent(request: ContentReplaceRequest): Promise<ContentReplaceResult>
  private getParameterData(dataId: string): Promise<ParameterData>
  private replaceTextContent(templateData: any, parameterData: ParameterData): any
  private preserveTextStyle(originalElement: any, newText: string): any
  private generatePreviewUrl(modifiedData: any): string
}
```

#### 2.2.3 外部API调用服务
```typescript
// service/src/services/externalApi.ts
export interface ExternalApiConfig {
  baseUrl: string
  apiKey: string
  timeout: number
}

export interface ParameterData {
  id: string
  configId: string
  parameterValues: Record<string, any>
  templateId: string
}

export interface ParameterConfig {
  id: string
  templateId: string
  parameters: ParameterDefinition[]
}

export class ExternalApiService {
  constructor(config: ExternalApiConfig)
  async getParameterData(dataId: string): Promise<ParameterData>
  async getParameterConfig(configId: string): Promise<ParameterConfig>
  async validateApiConnection(): Promise<boolean>
  private makeRequest<T>(endpoint: string, options?: RequestOptions): Promise<T>
}
```

### 2.3 API接口设计

#### 2.3.1 模板解析接口
```typescript
/**
 * @api {POST} /api/template/parse 解析模板内容
 * @apiName ParseTemplate
 * @apiGroup Template
 * 
 * @apiParam {String} templateId 模板ID
 * 
 * @apiSuccess {Object} data 解析结果
 * @apiSuccess {String} data.templateId 模板ID
 * @apiSuccess {String} data.templateTitle 模板标题
 * @apiSuccess {Array} data.textElements 文本元素列表
 * @apiSuccess {Array} data.parameterCandidates 参数候选项列表
 */
POST /api/template/parse
{
  "templateId": "2"
}

Response:
{
  "code": 200,
  "data": {
    "templateId": "2",
    "templateTitle": "示例模板 - 日签插画手机海报",
    "textElements": [...],
    "parameterCandidates": [...]
  }
}
```

#### 2.3.2 参数化预览接口
```typescript
/**
 * @api {GET} /preview/parameter/:dataId 参数化预览页面
 * @apiName ParameterPreview
 * @apiGroup Preview
 * 
 * @apiParam {String} dataId 参数数据ID
 */
GET /preview/parameter/:dataId

/**
 * @api {POST} /api/parameter/preview 生成预览数据
 * @apiName GeneratePreview
 * @apiGroup Parameter
 * 
 * @apiParam {String} templateId 模板ID
 * @apiParam {String} parameterDataId 参数数据ID
 */
POST /api/parameter/preview
{
  "templateId": "2",
  "parameterDataId": "user-data-123"
}

Response:
{
  "code": 200,
  "data": {
    "previewUrl": "/preview/parameter/user-data-123",
    "modifiedTemplateData": {...}
  }
}
```

#### 2.3.3 图片生成接口
```typescript
/**
 * @api {GET} /api/screenshots 扩展现有截图接口
 * @apiName GenerateImage
 * @apiGroup Screenshot
 * 
 * @apiParam {String} [parameterDataId] 参数数据ID
 * @apiParam {Number} [width] 图片宽度
 * @apiParam {Number} [height] 图片高度
 * @apiParam {String} [type=file] 输出类型
 * @apiParam {Number} [size=2] 图片尺寸倍数
 * @apiParam {Number} [quality=0.9] 图片质量
 */
GET /api/screenshots?parameterDataId=user-data-123&width=1242&height=2208&type=file&size=2&quality=0.9

/**
 * @api {POST} /api/parameter/batch-generate 批量生成图片
 * @apiName BatchGenerate
 * @apiGroup Parameter
 */
POST /api/parameter/batch-generate
{
  "dataIds": ["user-data-123", "user-data-124"],
  "outputOptions": {
    "width": 1242,
    "height": 2208,
    "type": "file",
    "size": 2,
    "quality": 0.9
  }
}
```

### 2.4 配置文件扩展

#### 2.4.1 环境变量配置
```typescript
// service/src/configs.ts (扩展)
export interface ExternalApiConfig {
  baseUrl: string
  apiKey: string
  timeout: number
  retryAttempts: number
}

export const externalApiConfig: ExternalApiConfig = {
  baseUrl: process.env.EXTERNAL_API_URL || 'http://localhost:3000/api',
  apiKey: process.env.EXTERNAL_API_KEY || '',
  timeout: parseInt(process.env.API_TIMEOUT || '10000'),
  retryAttempts: parseInt(process.env.API_RETRY_ATTEMPTS || '3')
}

export const parameterConfig = {
  cacheEnabled: process.env.PARAMETER_CACHE_ENABLED === 'true',
  cacheExpiration: parseInt(process.env.PARAMETER_CACHE_EXPIRATION || '3600'),
  maxBatchSize: parseInt(process.env.MAX_BATCH_SIZE || '50')
}
```

#### 2.4.2 环境变量文件
```bash
# service/.env
NODE_ENV=development
PORT=7001

# 外部API配置
EXTERNAL_API_URL=http://localhost:3000/api
EXTERNAL_API_KEY=your-secure-api-key
API_TIMEOUT=10000
API_RETRY_ATTEMPTS=3

# 参数化功能配置
PARAMETER_CACHE_ENABLED=true
PARAMETER_CACHE_EXPIRATION=3600
MAX_BATCH_SIZE=50

# 现有配置保持不变
WEBSITE_URL=http://127.0.0.1:5173/
FILE_PATH=/cache/
```

## 3. 类型定义

### 3.1 核心类型
```typescript
// service/src/types/parameter.d.ts
export enum ParameterType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  EMAIL = 'email',
  PHONE = 'phone',
  URL = 'url'
}

export enum TextCategory {
  PHONE = 'phone',
  ADDRESS = 'address',
  EMAIL = 'email',
  DATE = 'date',
  GENERAL = 'general'
}

export interface ElementPosition {
  left: number
  top: number
  width: number
  height: number
}

export interface ElementStyle {
  fontSize: number
  color: string
  fontFamily: string
  textAlign: string
  lineHeight: number
  letterSpacing: number
  fontWeight: string | number
  fontStyle: string
}

export interface ParameterDefinition {
  id: string
  elementUuid: string
  parameterName: string
  parameterLabel: string
  parameterType: ParameterType
  isRequired: boolean
  defaultValue?: string
  validationRules?: ValidationRule
  displayOrder: number
  isEnabled: boolean
}

export interface ValidationRule {
  minLength?: number
  maxLength?: number
  pattern?: string
  errorMessage?: string
}
```

## 4. 实现细节

### 4.1 模板解析实现

#### 4.1.1 文本元素识别算法
```typescript
// service/src/services/templateParser.ts
export class TemplateParserService {
  async parseTemplate(templateId: string): Promise<TemplateParseResult> {
    // 1. 获取模板数据
    const templateData = await this.getTemplateData(templateId)

    // 2. 解析模板结构
    const parsedData = this.parseTemplateStructure(templateData)

    // 3. 提取文本元素
    const textElements = this.extractTextElements(parsedData)

    // 4. 生成参数候选项
    const parameterCandidates = this.generateParameterCandidates(textElements)

    return {
      templateId,
      templateTitle: templateData.title,
      textElements,
      parameterCandidates
    }
  }

  private parseTemplateStructure(templateData: any): any {
    // 处理多页面模板格式
    let content = JSON.parse(templateData.data)

    if (Array.isArray(content)) {
      // 多页面模板，取第一页
      const { global, layers } = content[0]
      content = { page: global, widgets: layers }
    }

    return content
  }

  private extractTextElements(templateData: any): TextElement[] {
    const textElements: TextElement[] = []
    const layers = templateData.widgets || templateData.layers || []

    layers.forEach((layer: any) => {
      if (layer.type === 'w-text' && layer.text) {
        textElements.push({
          uuid: layer.uuid,
          type: layer.type,
          text: layer.text,
          position: {
            left: layer.left,
            top: layer.top,
            width: layer.width,
            height: layer.height
          },
          style: {
            fontSize: layer.fontSize,
            color: layer.color,
            fontFamily: layer.fontFamily,
            textAlign: layer.textAlign,
            lineHeight: layer.lineHeight,
            letterSpacing: layer.letterSpacing,
            fontWeight: layer.fontWeight,
            fontStyle: layer.fontStyle
          }
        })
      }
    })

    return textElements
  }

  private generateParameterCandidates(textElements: TextElement[]): ParameterCandidate[] {
    return textElements.map((element, index) => {
      const category = this.analyzeTextCategory(element.text)
      const suggestedName = this.suggestParameterName(element.text, category, index)
      const suggestedLabel = this.suggestParameterLabel(element.text, category)
      const suggestedType = this.suggestParameterType(category)

      return {
        elementUuid: element.uuid,
        suggestedName,
        suggestedLabel,
        suggestedType,
        originalText: element.text,
        textCategory: category
      }
    })
  }

  private analyzeTextCategory(text: string): TextCategory {
    // 移除HTML标签进行分析
    const cleanText = text.replace(/<[^>]*>/g, '')

    if (/电话|手机|tel|phone/i.test(cleanText)) {
      return TextCategory.PHONE
    }
    if (/地址|address|addr/i.test(cleanText)) {
      return TextCategory.ADDRESS
    }
    if /@.*\./i.test(cleanText)) {
      return TextCategory.EMAIL
    }
    if (/\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{1,2}月\d{1,2}日/i.test(cleanText)) {
      return TextCategory.DATE
    }

    return TextCategory.GENERAL
  }

  private suggestParameterName(text: string, category: TextCategory, index: number): string {
    const cleanText = text.replace(/<[^>]*>/g, '').toLowerCase()

    switch (category) {
      case TextCategory.PHONE:
        return 'phone'
      case TextCategory.ADDRESS:
        return 'address'
      case TextCategory.EMAIL:
        return 'email'
      case TextCategory.DATE:
        return 'date'
      default:
        // 根据文本内容生成参数名
        if (cleanText.includes('你好') || cleanText.includes('hello')) {
          return 'greeting'
        }
        if (cleanText.includes('名言') || cleanText.includes('quote')) {
          return 'quote'
        }
        if (cleanText.includes('标题') || cleanText.includes('title')) {
          return 'title'
        }
        return `text_${index + 1}`
    }
  }

  private suggestParameterLabel(text: string, category: TextCategory): string {
    switch (category) {
      case TextCategory.PHONE:
        return '联系电话'
      case TextCategory.ADDRESS:
        return '详细地址'
      case TextCategory.EMAIL:
        return '邮箱地址'
      case TextCategory.DATE:
        return '日期时间'
      default:
        return '文本内容'
    }
  }

  private suggestParameterType(category: TextCategory): ParameterType {
    switch (category) {
      case TextCategory.PHONE:
        return ParameterType.PHONE
      case TextCategory.ADDRESS:
        return ParameterType.TEXTAREA
      case TextCategory.EMAIL:
        return ParameterType.EMAIL
      default:
        return ParameterType.TEXT
    }
  }
}
```

### 4.2 参数替换引擎实现

#### 4.2.1 内容替换算法
```typescript
// service/src/services/parameterEngine.ts
export class ParameterEngineService {
  constructor(private externalApi: ExternalApiService) {}

  async replaceContent(request: ContentReplaceRequest): Promise<ContentReplaceResult> {
    // 1. 获取模板数据
    const templateData = await this.getTemplateData(request.templateId)

    // 2. 获取参数数据
    const parameterData = await this.externalApi.getParameterData(request.parameterDataId)

    // 3. 获取参数配置
    const parameterConfig = await this.externalApi.getParameterConfig(parameterData.configId)

    // 4. 执行内容替换
    const modifiedTemplateData = this.replaceTextContent(templateData, parameterData, parameterConfig)

    // 5. 生成预览URL
    const previewUrl = this.generatePreviewUrl(request.parameterDataId)

    return {
      modifiedTemplateData,
      previewUrl
    }
  }

  private replaceTextContent(templateData: any, parameterData: ParameterData, config: ParameterConfig): any {
    // 深拷贝模板数据
    const modifiedData = JSON.parse(JSON.stringify(templateData))
    let content = JSON.parse(modifiedData.data)

    // 处理多页面格式
    if (Array.isArray(content)) {
      content = content.map(page => this.replacePageContent(page, parameterData, config))
    } else {
      content = this.replacePageContent(content, parameterData, config)
    }

    modifiedData.data = JSON.stringify(content)
    return modifiedData
  }

  private replacePageContent(pageData: any, parameterData: ParameterData, config: ParameterConfig): any {
    const layers = pageData.layers || pageData.widgets || []

    layers.forEach((layer: any) => {
      if (layer.type === 'w-text') {
        // 查找对应的参数定义
        const paramDef = config.parameters.find(p => p.elementUuid === layer.uuid)
        if (paramDef && paramDef.isEnabled) {
          const newValue = parameterData.parameterValues[paramDef.parameterName]
          if (newValue !== undefined) {
            // 替换文本内容，保持HTML格式
            layer.text = this.formatTextContent(newValue, layer.text)
          }
        }
      }
    })

    return pageData
  }

  private formatTextContent(newValue: string, originalText: string): string {
    // 如果原文本包含HTML标签，保持格式
    if (originalText.includes('<br/>') || originalText.includes('<br>')) {
      // 将换行符转换为<br/>标签
      return newValue.replace(/\n/g, '<br/>')
    }

    return newValue
  }

  private generatePreviewUrl(parameterDataId: string): string {
    return `/preview/parameter/${parameterDataId}`
  }
}
```

### 4.3 外部API集成实现

#### 4.3.1 API调用服务
```typescript
// service/src/services/externalApi.ts
export class ExternalApiService {
  private config: ExternalApiConfig
  private cache: Map<string, any> = new Map()

  constructor(config: ExternalApiConfig) {
    this.config = config
  }

  async getParameterData(dataId: string): Promise<ParameterData> {
    const cacheKey = `parameter_data_${dataId}`

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const data = await this.makeRequest<ParameterData>(`/external/parameter-data/${dataId}`)

    // 缓存数据
    this.cache.set(cacheKey, data)
    setTimeout(() => this.cache.delete(cacheKey), this.config.timeout)

    return data
  }

  async getParameterConfig(configId: string): Promise<ParameterConfig> {
    const cacheKey = `parameter_config_${configId}`

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const config = await this.makeRequest<ParameterConfig>(`/external/parameter-config/${configId}`)

    // 缓存配置
    this.cache.set(cacheKey, config)
    setTimeout(() => this.cache.delete(cacheKey), this.config.timeout * 2) // 配置缓存时间更长

    return config
  }

  async validateApiConnection(): Promise<boolean> {
    try {
      await this.makeRequest('/external/health')
      return true
    } catch (error) {
      console.error('External API connection failed:', error)
      return false
    }
  }

  private async makeRequest<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`
    const requestOptions: RequestInit = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        ...options.headers
      },
      timeout: this.config.timeout,
      ...options
    }

    if (options.body) {
      requestOptions.body = JSON.stringify(options.body)
    }

    let lastError: Error

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, requestOptions)

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()

        if (result.code !== 200) {
          throw new Error(`API Error: ${result.message || 'Unknown error'}`)
        }

        return result.data
      } catch (error) {
        lastError = error as Error

        if (attempt < this.config.retryAttempts) {
          // 指数退避重试
          const delay = Math.pow(2, attempt - 1) * 1000
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw lastError
  }
}

interface RequestOptions {
  method?: string
  headers?: Record<string, string>
  body?: any
}
```

### 4.4 控制器实现

#### 4.4.1 参数化控制器
```typescript
// service/src/controllers/parameterController.ts
import { Request, Response } from 'express'
import { TemplateParserService } from '../services/templateParser'
import { ParameterEngineService } from '../services/parameterEngine'
import { ExternalApiService } from '../services/externalApi'
import { externalApiConfig } from '../configs'
import * as send from '../utils/send'

export class ParameterController {
  private templateParser: TemplateParserService
  private parameterEngine: ParameterEngineService
  private externalApi: ExternalApiService

  constructor() {
    this.externalApi = new ExternalApiService(externalApiConfig)
    this.templateParser = new TemplateParserService()
    this.parameterEngine = new ParameterEngineService(this.externalApi)
  }

  /**
   * @api {POST} /api/template/parse 解析模板内容
   */
  async parseTemplate(req: Request, res: Response) {
    try {
      const { templateId } = req.body

      if (!templateId) {
        return send.error(res, 'Template ID is required', 400)
      }

      const result = await this.templateParser.parseTemplate(templateId)
      send.success(res, result)
    } catch (error) {
      console.error('Parse template error:', error)
      send.error(res, 'Failed to parse template', 500)
    }
  }

  /**
   * @api {POST} /api/parameter/preview 生成预览数据
   */
  async generatePreview(req: Request, res: Response) {
    try {
      const { templateId, parameterDataId } = req.body

      if (!templateId || !parameterDataId) {
        return send.error(res, 'Template ID and parameter data ID are required', 400)
      }

      const result = await this.parameterEngine.replaceContent({
        templateId,
        parameterDataId
      })

      send.success(res, result)
    } catch (error) {
      console.error('Generate preview error:', error)
      send.error(res, 'Failed to generate preview', 500)
    }
  }

  /**
   * @api {GET} /preview/parameter/:dataId 参数化预览页面
   */
  async previewPage(req: Request, res: Response) {
    try {
      const { dataId } = req.params

      if (!dataId) {
        return res.status(400).send('Parameter data ID is required')
      }

      // 获取参数数据
      const parameterData = await this.externalApi.getParameterData(dataId)

      // 生成替换后的模板数据
      const result = await this.parameterEngine.replaceContent({
        templateId: parameterData.templateId,
        parameterDataId: dataId
      })

      // 渲染预览页面
      const previewHtml = this.generatePreviewHtml(result.modifiedTemplateData)
      res.send(previewHtml)
    } catch (error) {
      console.error('Preview page error:', error)
      res.status(500).send('Failed to load preview page')
    }
  }

  /**
   * @api {POST} /api/parameter/batch-generate 批量生成图片
   */
  async batchGenerate(req: Request, res: Response) {
    try {
      const { dataIds, outputOptions } = req.body

      if (!dataIds || !Array.isArray(dataIds) || dataIds.length === 0) {
        return send.error(res, 'Data IDs array is required', 400)
      }

      if (dataIds.length > 50) { // 限制批量大小
        return send.error(res, 'Batch size cannot exceed 50', 400)
      }

      const batchId = this.generateBatchId()

      // 异步处理批量生成
      this.processBatchGeneration(batchId, dataIds, outputOptions)

      send.success(res, { batchId, status: 'processing' })
    } catch (error) {
      console.error('Batch generate error:', error)
      send.error(res, 'Failed to start batch generation', 500)
    }
  }

  /**
   * @api {GET} /api/parameter/batch-status/:batchId 获取批量生成状态
   */
  async getBatchStatus(req: Request, res: Response) {
    try {
      const { batchId } = req.params

      // 这里应该从缓存或数据库中获取批量任务状态
      // 简化实现，实际应该使用Redis或数据库存储状态
      const status = this.getBatchStatusFromCache(batchId)

      send.success(res, status)
    } catch (error) {
      console.error('Get batch status error:', error)
      send.error(res, 'Failed to get batch status', 500)
    }
  }

  private generatePreviewHtml(templateData: any): string {
    // 生成预览页面HTML
    // 这里应该使用模板引擎或直接构建HTML
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>参数化预览</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body { margin: 0; padding: 20px; background: #f5f5f5; }
          .preview-container { max-width: 800px; margin: 0 auto; }
        </style>
      </head>
      <body>
        <div class="preview-container">
          <div id="design-board"></div>
        </div>
        <script>
          window.templateData = ${JSON.stringify(templateData)};
          // 这里应该加载前端预览组件
        </script>
      </body>
      </html>
    `
  }

  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private async processBatchGeneration(batchId: string, dataIds: string[], outputOptions: any) {
    // 实际实现应该使用队列系统处理
    // 这里简化为直接处理
    try {
      const results = []

      for (const dataId of dataIds) {
        try {
          // 生成单个图片
          const imageUrl = await this.generateSingleImage(dataId, outputOptions)
          results.push({ dataId, imageUrl, status: 'success' })
        } catch (error) {
          results.push({ dataId, error: error.message, status: 'failed' })
        }
      }

      // 更新批量任务状态
      this.updateBatchStatus(batchId, { status: 'completed', results })
    } catch (error) {
      this.updateBatchStatus(batchId, { status: 'failed', error: error.message })
    }
  }

  private async generateSingleImage(dataId: string, outputOptions: any): Promise<string> {
    // 调用现有的截图服务
    const screenshotUrl = `/api/screenshots?parameterDataId=${dataId}&width=${outputOptions.width}&height=${outputOptions.height}&type=${outputOptions.type}&size=${outputOptions.size}&quality=${outputOptions.quality}`

    // 这里应该实际调用截图服务
    // 返回生成的图片URL
    return `http://localhost:7001${screenshotUrl}`
  }

  private getBatchStatusFromCache(batchId: string): any {
    // 简化实现，实际应该从Redis或数据库获取
    return {
      batchId,
      status: 'processing',
      progress: 50,
      total: 10,
      completed: 5,
      failed: 0
    }
  }

  private updateBatchStatus(batchId: string, status: any) {
    // 简化实现，实际应该更新到Redis或数据库
    console.log(`Batch ${batchId} status updated:`, status)
  }
}
```

### 4.5 路由配置

#### 4.5.1 参数化路由
```typescript
// service/src/routes/parameter.ts
import { Router } from 'express'
import { ParameterController } from '../controllers/parameterController'

const router = Router()
const parameterController = new ParameterController()

// 模板解析
router.post('/template/parse', (req, res) => parameterController.parseTemplate(req, res))

// 预览相关
router.post('/parameter/preview', (req, res) => parameterController.generatePreview(req, res))
router.get('/preview/parameter/:dataId', (req, res) => parameterController.previewPage(req, res))

// 批量生成
router.post('/parameter/batch-generate', (req, res) => parameterController.batchGenerate(req, res))
router.get('/parameter/batch-status/:batchId', (req, res) => parameterController.getBatchStatus(req, res))

export default router
```

#### 4.5.2 主路由集成
```typescript
// service/src/control/router.ts (扩展现有路由)
import express from 'express'
import parameterRoutes from '../routes/parameter'
// ... 其他现有导入

const router = express.Router()

// 现有路由保持不变
// ...

// 新增参数化路由
router.use('/api', parameterRoutes)

export default router
```

### 4.6 截图服务扩展

#### 4.6.1 扩展现有截图接口
```typescript
// service/src/service/screenshots.ts (扩展现有文件)
import { ParameterEngineService } from '../services/parameterEngine'
import { ExternalApiService } from '../services/externalApi'

// 在现有截图函数中添加参数化支持
export async function screenshots(req: any, res: Response) {
  const { id, tempid, parameterDataId, width, height, type, size, quality } = req.query

  try {
    let targetUrl = ''

    if (parameterDataId) {
      // 参数化截图
      targetUrl = `${drawLink}/preview/parameter/${parameterDataId}`
    } else if (id || tempid) {
      // 现有逻辑保持不变
      targetUrl = `${drawLink}?id=${id || tempid}&tempType=${type || 0}`
    } else {
      return send.error(res, 'Missing required parameters', 400)
    }

    // 调用现有截图逻辑
    const result = await captureScreenshot({
      url: targetUrl,
      width: parseInt(width) || 1200,
      height: parseInt(height) || 800,
      size: parseInt(size) || 2,
      quality: parseFloat(quality) || 0.9,
      type: type || 'file'
    })

    send.success(res, result)
  } catch (error) {
    console.error('Screenshot error:', error)
    send.error(res, 'Failed to generate screenshot', 500)
  }
}
```

### 4.7 前端预览页面扩展

#### 4.7.1 预览路由扩展
```typescript
// src/router/index.ts (扩展现有路由)
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  // 现有路由保持不变
  // ...

  // 新增参数化预览路由
  {
    path: '/preview/parameter/:dataId',
    name: 'ParameterPreview',
    component: () => import('@/views/ParameterPreview.vue'),
    props: true
  }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

#### 4.7.2 参数化预览组件
```vue
<!-- src/views/ParameterPreview.vue -->
<template>
  <div class="parameter-preview">
    <div class="preview-container">
      <design-board
        v-if="templateData"
        :template-data="templateData"
        :preview-mode="true"
      />
      <div v-else class="loading">
        <el-loading text="正在加载预览..." />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import DesignBoard from '@/components/modules/layout/designBoard/index.vue'
import api from '@/api'

interface Props {
  dataId: string
}

const props = defineProps<Props>()
const route = useRoute()
const templateData = ref<any>(null)

onMounted(async () => {
  try {
    const dataId = props.dataId || route.params.dataId as string

    // 调用后端API获取参数化模板数据
    const response = await api.parameter.getPreviewData(dataId)
    templateData.value = response.data.modifiedTemplateData

    // 应用模板数据到画布
    applyTemplateData(templateData.value)
  } catch (error) {
    console.error('Failed to load preview data:', error)
  }
})

function applyTemplateData(data: any) {
  // 这里应该调用现有的模板加载逻辑
  // 类似于 Draw.vue 中的加载逻辑
  const content = JSON.parse(data.data)

  if (Array.isArray(content)) {
    const { global, layers } = content[0]
    // 应用到画布状态管理
    // canvasStore.setDPage(global)
    // widgetStore.setDWidgets(layers)
  }
}
</script>

<style lang="less" scoped>
.parameter-preview {
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;

  .preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}
</style>
```

## 5. 主项目集成指南

### 5.1 主项目需要实现的API接口

#### 5.1.1 外部API接口规范
```typescript
// 主项目需要实现的API接口

/**
 * @api {GET} /api/external/parameter-data/:dataId 获取参数数据
 * @apiName GetParameterData
 * @apiGroup External
 *
 * @apiHeader {String} Authorization Bearer token
 * @apiParam {String} dataId 参数数据ID
 *
 * @apiSuccess {Object} data 参数数据
 * @apiSuccess {String} data.id 数据ID
 * @apiSuccess {String} data.configId 配置ID
 * @apiSuccess {String} data.templateId 模板ID
 * @apiSuccess {Object} data.parameterValues 参数值对象
 */
GET /api/external/parameter-data/:dataId

Response:
{
  "code": 200,
  "data": {
    "id": "user-data-123",
    "configId": "config-456",
    "templateId": "2",
    "parameterValues": {
      "greeting": "你好,新年快乐",
      "quote": "成功不是终点，失败不是致命的，重要的是继续前进的勇气。",
      "contact": "电话：138-0000-0000\n地址：北京市朝阳区xxx路123号"
    }
  }
}

/**
 * @api {GET} /api/external/parameter-config/:configId 获取参数配置
 * @apiName GetParameterConfig
 * @apiGroup External
 *
 * @apiHeader {String} Authorization Bearer token
 * @apiParam {String} configId 配置ID
 *
 * @apiSuccess {Object} data 参数配置
 * @apiSuccess {String} data.id 配置ID
 * @apiSuccess {String} data.templateId 模板ID
 * @apiSuccess {Array} data.parameters 参数定义列表
 */
GET /api/external/parameter-config/:configId

Response:
{
  "code": 200,
  "data": {
    "id": "config-456",
    "templateId": "2",
    "parameters": [
      {
        "id": "param-1",
        "elementUuid": "98fd9b16db8a",
        "parameterName": "greeting",
        "parameterLabel": "个性问候语",
        "parameterType": "text",
        "isRequired": true,
        "defaultValue": "你好,十二月",
        "isEnabled": true
      }
    ]
  }
}

/**
 * @api {GET} /api/external/health 健康检查
 * @apiName HealthCheck
 * @apiGroup External
 *
 * @apiSuccess {String} status 服务状态
 */
GET /api/external/health

Response:
{
  "code": 200,
  "data": {
    "status": "ok",
    "timestamp": "2025-01-16T10:00:00Z"
  }
}
```

### 5.2 主项目数据库设计

#### 5.2.1 数据库表结构
```sql
-- 主项目数据库表设计

-- 模板参数配置表
CREATE TABLE poster_template_configs (
  id VARCHAR(32) PRIMARY KEY,
  template_id VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
  template_title VARCHAR(255) COMMENT '模板标题',
  config_name VARCHAR(255) NOT NULL COMMENT '配置名称',
  config_description TEXT COMMENT '配置描述',
  parameters JSON NOT NULL COMMENT '参数定义JSON',
  created_by VARCHAR(32) COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status TINYINT DEFAULT 1 COMMENT '状态：1启用 0禁用',
  INDEX idx_template_id (template_id),
  INDEX idx_created_by (created_by)
);

-- 用户参数数据表
CREATE TABLE poster_user_data (
  id VARCHAR(32) PRIMARY KEY,
  config_id VARCHAR(32) NOT NULL COMMENT '配置ID',
  user_id VARCHAR(32) COMMENT '用户ID',
  session_id VARCHAR(64) COMMENT '会话ID（匿名用户）',
  parameter_values JSON NOT NULL COMMENT '用户填写的参数值',
  is_draft BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
  preview_url VARCHAR(500) COMMENT '预览页面URL',
  generated_image_url VARCHAR(500) COMMENT '生成的图片URL',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_config_id (config_id),
  INDEX idx_user_id (user_id),
  INDEX idx_session_id (session_id),
  FOREIGN KEY (config_id) REFERENCES poster_template_configs(id) ON DELETE CASCADE
);

-- 图片生成记录表
CREATE TABLE poster_generation_records (
  id VARCHAR(32) PRIMARY KEY,
  data_id VARCHAR(32) NOT NULL COMMENT '参数数据ID',
  image_url VARCHAR(500) NOT NULL COMMENT '生成的图片URL',
  generation_options JSON COMMENT '生成选项',
  generation_time DECIMAL(10,3) COMMENT '生成耗时（秒）',
  file_size INT COMMENT '文件大小（字节）',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_data_id (data_id),
  FOREIGN KEY (data_id) REFERENCES poster_user_data(id) ON DELETE CASCADE
);
```

### 5.3 主项目API实现示例

#### 5.3.1 Node.js + Express 实现示例
```javascript
// 主项目 API 实现示例 (Node.js + Express)

const express = require('express')
const mysql = require('mysql2/promise')
const router = express.Router()

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'your_username',
  password: 'your_password',
  database: 'your_database'
}

// 中间件：验证API Key
function validateApiKey(req, res, next) {
  const apiKey = req.headers.authorization?.replace('Bearer ', '')

  if (!apiKey || apiKey !== process.env.POSTER_API_KEY) {
    return res.status(401).json({
      code: 401,
      message: 'Invalid API key'
    })
  }

  next()
}

// 获取参数数据
router.get('/external/parameter-data/:dataId', validateApiKey, async (req, res) => {
  try {
    const { dataId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    const [rows] = await connection.execute(
      `SELECT pd.*, pc.template_id
       FROM poster_user_data pd
       JOIN poster_template_configs pc ON pd.config_id = pc.id
       WHERE pd.id = ?`,
      [dataId]
    )

    await connection.end()

    if (rows.length === 0) {
      return res.status(404).json({
        code: 404,
        message: 'Parameter data not found'
      })
    }

    const data = rows[0]
    res.json({
      code: 200,
      data: {
        id: data.id,
        configId: data.config_id,
        templateId: data.template_id,
        parameterValues: JSON.parse(data.parameter_values)
      }
    })
  } catch (error) {
    console.error('Get parameter data error:', error)
    res.status(500).json({
      code: 500,
      message: 'Internal server error'
    })
  }
})

// 获取参数配置
router.get('/external/parameter-config/:configId', validateApiKey, async (req, res) => {
  try {
    const { configId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    const [rows] = await connection.execute(
      'SELECT * FROM poster_template_configs WHERE id = ? AND status = 1',
      [configId]
    )

    await connection.end()

    if (rows.length === 0) {
      return res.status(404).json({
        code: 404,
        message: 'Parameter config not found'
      })
    }

    const config = rows[0]
    res.json({
      code: 200,
      data: {
        id: config.id,
        templateId: config.template_id,
        parameters: JSON.parse(config.parameters)
      }
    })
  } catch (error) {
    console.error('Get parameter config error:', error)
    res.status(500).json({
      code: 500,
      message: 'Internal server error'
    })
  }
})

// 健康检查
router.get('/external/health', validateApiKey, (req, res) => {
  res.json({
    code: 200,
    data: {
      status: 'ok',
      timestamp: new Date().toISOString()
    }
  })
})

module.exports = router
```

#### 5.3.2 主项目前端管理界面示例
```vue
<!-- 主项目管理界面示例 -->
<template>
  <div class="parameter-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>参数化模板管理</span>
          <el-button type="primary" @click="createConfig">创建配置</el-button>
        </div>
      </template>

      <!-- 模板选择 -->
      <el-form :model="form" label-width="120px">
        <el-form-item label="选择模板">
          <el-select v-model="form.templateId" @change="parseTemplate">
            <el-option
              v-for="template in templates"
              :key="template.id"
              :label="template.title"
              :value="template.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 参数配置 -->
      <div v-if="parameterCandidates.length > 0" class="parameter-config">
        <h3>参数配置</h3>
        <el-table :data="parameterCandidates" border>
          <el-table-column prop="originalText" label="原始文本" width="200" />
          <el-table-column prop="suggestedLabel" label="参数标签" width="150">
            <template #default="{ row }">
              <el-input v-model="row.parameterLabel" size="small" />
            </template>
          </el-table-column>
          <el-table-column prop="suggestedName" label="参数名称" width="150">
            <template #default="{ row }">
              <el-input v-model="row.parameterName" size="small" />
            </template>
          </el-table-column>
          <el-table-column prop="suggestedType" label="参数类型" width="120">
            <template #default="{ row }">
              <el-select v-model="row.parameterType" size="small">
                <el-option label="文本" value="text" />
                <el-option label="多行文本" value="textarea" />
                <el-option label="电话" value="phone" />
                <el-option label="邮箱" value="email" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="是否开放" width="100">
            <template #default="{ row }">
              <el-switch v-model="row.isEnabled" />
            </template>
          </el-table-column>
          <el-table-column label="必填" width="80">
            <template #default="{ row }">
              <el-switch v-model="row.isRequired" />
            </template>
          </el-table-column>
        </el-table>

        <div class="actions">
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
          <el-button @click="previewForm">预览表单</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const form = reactive({
  templateId: '',
  configName: '',
  configDescription: ''
})

const templates = ref([])
const parameterCandidates = ref([])

// 解析模板
async function parseTemplate() {
  if (!form.templateId) return

  try {
    // 调用迅排设计服务解析模板
    const response = await api.poster.parseTemplate({
      templateId: form.templateId
    })

    parameterCandidates.value = response.data.parameterCandidates.map(candidate => ({
      ...candidate,
      parameterLabel: candidate.suggestedLabel,
      parameterName: candidate.suggestedName,
      parameterType: candidate.suggestedType,
      isEnabled: true,
      isRequired: false
    }))

    ElMessage.success('模板解析成功')
  } catch (error) {
    ElMessage.error('模板解析失败')
  }
}

// 保存配置
async function saveConfig() {
  try {
    const enabledParameters = parameterCandidates.value.filter(p => p.isEnabled)

    const configData = {
      templateId: form.templateId,
      configName: form.configName || `配置_${Date.now()}`,
      configDescription: form.configDescription,
      parameters: enabledParameters
    }

    await api.poster.saveConfig(configData)
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('配置保存失败')
  }
}

// 预览表单
function previewForm() {
  // 打开表单预览窗口
  const enabledParameters = parameterCandidates.value.filter(p => p.isEnabled)
  // 实现表单预览逻辑
}
</script>
```

## 6. 部署和配置

### 6.1 迅排设计服务部署

#### 6.1.1 环境变量配置
```bash
# service/.env
NODE_ENV=production
PORT=7001

# 外部API配置
EXTERNAL_API_URL=https://your-main-project.com/api
EXTERNAL_API_KEY=your-secure-api-key-here
API_TIMEOUT=10000
API_RETRY_ATTEMPTS=3

# 参数化功能配置
PARAMETER_CACHE_ENABLED=true
PARAMETER_CACHE_EXPIRATION=3600
MAX_BATCH_SIZE=50

# 现有配置
WEBSITE_URL=https://your-poster-frontend.com/
FILE_PATH=/var/www/poster-cache/
```

#### 6.1.2 Docker部署配置
```dockerfile
# service/Dockerfile (扩展现有Dockerfile)
FROM node:20-alpine AS builder

WORKDIR /usr/src/app
COPY ./package*.json ./
RUN npm install --registry=https://registry.npmmirror.com

COPY ./ ./
RUN npm run build && cp -r ./src/mock ./dist

FROM ghcr.io/puppeteer/puppeteer:latest

USER root
RUN mkdir -p /cache

WORKDIR /usr/src/app
COPY --from=builder /usr/src/app/dist ./
RUN npm install --registry=https://registry.npmmirror.com

# 添加环境变量支持
ENV NODE_ENV=production
ENV EXTERNAL_API_URL=""
ENV EXTERNAL_API_KEY=""

RUN mkdir -p server && mv -f server.js server/index.js

EXPOSE 7001

CMD ["node", "server/index.js"]
```

### 6.2 主项目集成部署

#### 6.2.1 API Key生成和管理
```javascript
// 主项目中生成和管理API Key
const crypto = require('crypto')

// 生成API Key
function generateApiKey() {
  return crypto.randomBytes(32).toString('hex')
}

// 验证API Key
function validateApiKey(apiKey) {
  // 从数据库或配置中验证API Key
  const validKeys = process.env.VALID_API_KEYS?.split(',') || []
  return validKeys.includes(apiKey)
}

// 环境变量配置
// POSTER_API_KEY=your-generated-api-key
// VALID_API_KEYS=key1,key2,key3
```

## 7. 测试指南

### 7.1 单元测试

#### 7.1.1 模板解析服务测试
```typescript
// service/src/tests/templateParser.test.ts
import { TemplateParserService } from '../services/templateParser'

describe('TemplateParserService', () => {
  let service: TemplateParserService

  beforeEach(() => {
    service = new TemplateParserService()
  })

  test('should parse template and extract text elements', async () => {
    const result = await service.parseTemplate('2')

    expect(result.templateId).toBe('2')
    expect(result.textElements).toHaveLength(5)
    expect(result.parameterCandidates).toHaveLength(5)
  })

  test('should categorize text correctly', () => {
    const phoneText = '电话：138-0000-0000'
    const category = service.analyzeTextCategory(phoneText)
    expect(category).toBe(TextCategory.PHONE)
  })
})
```

### 7.2 集成测试

#### 7.2.1 API集成测试
```javascript
// 集成测试示例
const request = require('supertest')
const app = require('../app')

describe('Parameter API Integration', () => {
  test('should parse template successfully', async () => {
    const response = await request(app)
      .post('/api/template/parse')
      .send({ templateId: '2' })
      .expect(200)

    expect(response.body.code).toBe(200)
    expect(response.body.data.textElements).toBeDefined()
  })

  test('should generate preview successfully', async () => {
    const response = await request(app)
      .post('/api/parameter/preview')
      .send({
        templateId: '2',
        parameterDataId: 'test-data-123'
      })
      .expect(200)

    expect(response.body.data.previewUrl).toBeDefined()
  })
})
```

## 8. 监控和日志

### 8.1 日志配置
```typescript
// service/src/utils/logger.ts
import winston from 'winston'

export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
})
```

### 8.2 性能监控
```typescript
// service/src/middleware/performance.ts
export function performanceMonitor(req: Request, res: Response, next: NextFunction) {
  const start = Date.now()

  res.on('finish', () => {
    const duration = Date.now() - start
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`
    })
  })

  next()
}
```

## 9. 开发实施指南

### 9.1 立即开始开发

#### 9.1.1 第一步：创建项目结构
```bash
# 在 service/src/ 目录下创建新的目录结构
mkdir -p service/src/services
mkdir -p service/src/controllers
mkdir -p service/src/types
mkdir -p service/src/routes

# 创建核心文件
touch service/src/services/templateParser.ts
touch service/src/services/parameterEngine.ts
touch service/src/services/externalApi.ts
touch service/src/controllers/parameterController.ts
touch service/src/routes/parameter.ts
touch service/src/types/parameter.d.ts
```

#### 9.1.2 第二步：实现模板解析服务
按照第4.1节的代码示例，实现 `service/src/services/templateParser.ts`：

1. 复制第4.1节中的 `TemplateParserService` 类代码
2. 实现 `parseTemplate` 方法
3. 实现文本元素识别算法
4. 实现参数候选项生成算法

#### 9.1.3 第三步：创建API接口
按照第4.4节的代码示例，实现 `service/src/controllers/parameterController.ts`：

1. 复制 `ParameterController` 类代码
2. 实现 `parseTemplate` 方法
3. 创建路由配置

#### 9.1.4 第四步：测试验证
使用模板ID=2进行测试：

```bash
# 启动服务
npm run serve

# 测试模板解析接口
curl -X POST http://localhost:7001/api/template/parse \
  -H "Content-Type: application/json" \
  -d '{"templateId": "2"}'
```

### 9.2 开发检查清单

#### 阶段一：模板解析引擎（2周）
- [ ] 创建项目结构
- [ ] 实现 `TemplateParserService` 类
- [ ] 实现文本元素识别算法
- [ ] 实现参数候选项生成
- [ ] 创建 `POST /api/template/parse` 接口
- [ ] 编写单元测试
- [ ] 使用模板ID=2验证功能

#### 阶段二：外部API集成（2周）
- [ ] 实现 `ExternalApiService` 类
- [ ] 添加API重试和缓存机制
- [ ] 实现健康检查功能
- [ ] 编写集成测试

#### 阶段三：参数替换和预览（2周）
- [ ] 实现 `ParameterEngineService` 类
- [ ] 创建参数化预览页面
- [ ] 实现 `POST /api/parameter/preview` 接口
- [ ] 扩展前端预览组件

#### 阶段四：图片生成扩展（1周）
- [ ] 扩展截图接口支持 `parameterDataId`
- [ ] 实现批量生成功能
- [ ] 优化性能

#### 阶段五：集成文档（1周）
- [ ] 编写主项目集成指南
- [ ] 提供API实现示例
- [ ] 编写部署文档

### 9.3 关键代码位置

#### 现有代码文件（需要了解）
- **模板数据获取**：`service/src/service/design.ts` 中的 `getDetail` 函数
- **截图服务**：`service/src/service/screenshots.ts`
- **路由配置**：`service/src/control/router.ts`
- **配置文件**：`service/src/configs.ts`

#### 新增代码文件（需要创建）
- **模板解析服务**：`service/src/services/templateParser.ts`
- **参数替换引擎**：`service/src/services/parameterEngine.ts`
- **外部API服务**：`service/src/services/externalApi.ts`
- **参数控制器**：`service/src/controllers/parameterController.ts`
- **参数路由**：`service/src/routes/parameter.ts`
- **类型定义**：`service/src/types/parameter.d.ts`

### 9.4 重要提醒

1. **不要修改现有功能**：只扩展，不修改现有代码
2. **使用现有工具**：复用现有的文件读取、错误处理等工具
3. **保持一致性**：遵循现有的代码风格和目录结构
4. **测试驱动**：每个功能都要有对应的测试用例
5. **文档同步**：代码实现后及时更新API文档

---

**文档版本**: v1.1
**创建日期**: 2025-01-16
**最后更新**: 2025-01-16
**文档作者**: AI Assistant
**审核状态**: 待审核
