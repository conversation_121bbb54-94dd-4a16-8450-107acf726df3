{"name": "screenshot-node", "version": "1.0.0", "description": "", "main": "./src/main", "scripts": {"dev": "cross-env NODE_ENV=development ts-node-dev src/main", "build": "cross-env NODE_ENV=production webpack", "serve": "ts-node src/main", "start": "webpack --watch", "serverstart": "pm2 start ./dist/server.js --watch", "tscstart": "tsc -w", "build:apidoc": "apidoc -i src/ -o _apidoc/", "publish": "rm -rf ./static && rm -rf ./_apidoc && sh script/publish.sh", "publish-fast": "git add . && git commit -m 'build: auto publish' && npm run publish"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@types/cors": "^2.8.19", "axios": "^1.7.3", "body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.19.2", "image-size": "^1.1.1", "images": "^3.2.4", "multiparty": "^4.2.3", "puppeteer": "^10.4.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/multiparty": "^4.2.1", "@types/node": "^16.18.105", "cross-env": "^7.0.3", "ts-loader": "^6.0.4", "ts-node": "^8.3.0", "ts-node-dev": "^2.0.0", "typescript": "^3.5.3", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "apidoc": {"title": "自动api接口文档", "url": "http://localhost:9999/", "sampleUrl": "http://localhost:9999/"}}