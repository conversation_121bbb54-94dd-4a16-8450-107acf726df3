/**
 * 权限策略修复工具
 * 用于解决第三方脚本的权限策略违规问题
 */

interface PermissionsPolicyConfig {
  filterConsoleWarnings: boolean
  overrideUnloadEvents: boolean
  enableAlternativeTracking: boolean
  analyticsEndpoint?: string
}

class PermissionsPolicyFix {
  private config: PermissionsPolicyConfig
  private originalConsoleWarn: typeof console.warn
  private originalAddEventListener: typeof window.addEventListener

  constructor(config: Partial<PermissionsPolicyConfig> = {}) {
    this.config = {
      filterConsoleWarnings: true,
      overrideUnloadEvents: true,
      enableAlternativeTracking: true,
      analyticsEndpoint: undefined, // 默认不发送到后端
      ...config
    }

    this.originalConsoleWarn = console.warn
    this.originalAddEventListener = window.addEventListener.bind(window)
  }

  /**
   * 应用所有修复措施
   */
  apply() {
    if (this.config.filterConsoleWarnings) {
      this.filterConsoleWarnings()
    }

    if (this.config.overrideUnloadEvents) {
      this.overrideUnloadEvents()
    }

    if (this.config.enableAlternativeTracking) {
      this.enableAlternativeTracking()
    }

    // 只在开发环境显示成功信息
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 权限策略修复已应用')
    }
  }

  /**
   * 过滤控制台权限策略警告
   */
  private filterConsoleWarnings() {
    console.warn = (...args: any[]) => {
      const message = args.join(' ')
      
      // 过滤权限策略相关的警告
      const shouldFilter = [
        'Permissions policy violation',
        'unload is not allowed',
        'beforeunload is not allowed'
      ].some(keyword => message.includes(keyword))

      if (!shouldFilter) {
        this.originalConsoleWarn.apply(console, args)
      }
    }
  }

  /**
   * 重写 unload 事件监听器
   */
  private overrideUnloadEvents() {
    window.addEventListener = (type: string, listener: any, options?: any) => {
      // 将 unload 事件转换为更安全的替代方案
      if (type === 'unload') {
        // 使用 beforeunload 替代
        this.originalAddEventListener('beforeunload', listener, options)
        // 同时使用 visibilitychange 作为备用
        this.originalAddEventListener('visibilitychange', (event) => {
          if (document.visibilityState === 'hidden') {
            listener(event)
          }
        }, options)
        return
      }

      // 其他事件正常处理
      this.originalAddEventListener(type, listener, options)
    }
  }

  /**
   * 启用替代的页面追踪方案
   */
  private enableAlternativeTracking() {
    // 页面可见性变化追踪
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.sendPageLeaveEvent()
      }
    })

    // 页面隐藏事件（更可靠）
    window.addEventListener('pagehide', () => {
      this.sendPageLeaveEvent()
    })

    // beforeunload 作为最后的备用
    window.addEventListener('beforeunload', () => {
      this.sendPageLeaveEvent()
    })
  }

  /**
   * 发送页面离开事件
   */
  private sendPageLeaveEvent() {
    try {
      // 如果有百度统计，发送事件
      if ((window as any)._hmt) {
        (window as any)._hmt.push(['_trackEvent', 'page', 'leave', location.pathname])
      }

      // 只有配置了分析端点才发送数据
      if (this.config.analyticsEndpoint && navigator.sendBeacon) {
        const data = JSON.stringify({
          event: 'page_leave',
          url: location.href,
          timestamp: Date.now()
        })
        navigator.sendBeacon(this.config.analyticsEndpoint, data)
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 恢复原始行为
   */
  restore() {
    console.warn = this.originalConsoleWarn
    window.addEventListener = this.originalAddEventListener

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 权限策略修复已恢复')
    }
  }
}

// 创建全局实例
const permissionsPolicyFix = new PermissionsPolicyFix()

export default permissionsPolicyFix
