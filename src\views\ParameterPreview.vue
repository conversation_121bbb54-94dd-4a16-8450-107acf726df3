<template>
  <div class="parameter-preview">
    <div class="preview-header">
      <h2>参数化预览</h2>
      <div class="preview-info">
        <span>模板ID: {{ templateId }}</span>
        <span>数据ID: {{ dataId }}</span>
      </div>
    </div>
    
    <div class="preview-content">
      <div v-if="loading" class="loading">
        <el-loading text="正在生成预览..." />
      </div>
      
      <div v-else-if="error" class="error">
        <el-alert
          :title="error"
          type="error"
          show-icon
          :closable="false"
        />
      </div>
      
      <div v-else class="preview-canvas">
        <!-- 这里可以集成现有的画布组件 -->
        <div class="canvas-container" ref="canvasContainer">
          <div class="template-preview" v-html="previewHtml"></div>
        </div>
        
        <div class="preview-actions">
          <el-button @click="refreshPreview" :loading="loading">
            刷新预览
          </el-button>
          <el-button type="primary" @click="generateImage" :loading="generating">
            生成图片
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElLoading, ElAlert, ElButton, ElMessage } from 'element-plus'
import axios from 'axios'

// 路由参数
const route = useRoute()
const dataId = computed(() => route.params.dataId as string)
const templateId = ref('')

// 响应式数据
const loading = ref(false)
const generating = ref(false)
const error = ref('')
const previewHtml = ref('')
const templateData = ref<any>(null)

// 获取预览数据
async function loadPreviewData() {
  if (!dataId.value) {
    error.value = '缺少参数数据ID'
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 首先获取参数数据以获取模板ID
    const paramDataResponse = await axios.get(`/api/external/parameter-data/${dataId.value}`)
    templateId.value = paramDataResponse.data.data.templateId

    // 生成预览
    const previewResponse = await axios.post('/api/parameter/preview', {
      templateId: templateId.value,
      parameterDataId: dataId.value
    })

    templateData.value = previewResponse.data.data.modifiedTemplateData
    
    // 应用模板数据到画布
    await applyTemplateData(templateData.value)
    
    ElMessage.success('预览加载成功')
  } catch (err: any) {
    console.error('Load preview error:', err)
    error.value = err.response?.data?.message || '加载预览失败'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 应用模板数据到画布
async function applyTemplateData(data: any) {
  try {
    // 这里可以集成现有的画布渲染逻辑
    // 暂时使用简单的HTML展示
    const content = JSON.parse(data.data)
    let html = '<div class="template-content">'
    
    if (Array.isArray(content)) {
      // 多页面模板
      content.forEach((page, index) => {
        html += `<div class="page" data-page="${index}">`
        html += renderPageContent(page)
        html += '</div>'
      })
    } else {
      // 单页面模板
      html += renderPageContent(content)
    }
    
    html += '</div>'
    previewHtml.value = html
  } catch (err) {
    console.error('Apply template data error:', err)
    throw new Error('应用模板数据失败')
  }
}

// 渲染页面内容
function renderPageContent(pageData: any): string {
  const layers = pageData.layers || pageData.widgets || []
  let html = '<div class="page-content">'
  
  layers.forEach((layer: any) => {
    if (layer.type === 'w-text') {
      html += `
        <div class="text-element" style="
          position: absolute;
          left: ${layer.left}px;
          top: ${layer.top}px;
          width: ${layer.width}px;
          height: ${layer.height}px;
          font-size: ${layer.fontSize}px;
          color: ${layer.color};
          font-family: ${layer.fontFamily || 'Arial'};
          text-align: ${layer.textAlign || 'left'};
          line-height: ${layer.lineHeight || 1.2};
          font-weight: ${layer.fontWeight || 'normal'};
        ">
          ${layer.text}
        </div>
      `
    }
  })
  
  html += '</div>'
  return html
}

// 刷新预览
async function refreshPreview() {
  await loadPreviewData()
}

// 生成图片
async function generateImage() {
  if (!templateId.value || !dataId.value) {
    ElMessage.error('缺少必要参数')
    return
  }

  generating.value = true

  try {
    // 调用截图API生成图片
    const response = await axios.get('/api/screenshots', {
      params: {
        url: `/preview/parameter/${dataId.value}`,
        width: 800,
        height: 600
      }
    })

    // 处理生成的图片
    if (response.data.success) {
      ElMessage.success('图片生成成功')
      // 可以在这里处理图片下载或显示
    } else {
      throw new Error(response.data.message || '图片生成失败')
    }
  } catch (err: any) {
    console.error('Generate image error:', err)
    ElMessage.error(err.response?.data?.message || '图片生成失败')
  } finally {
    generating.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadPreviewData()
})
</script>

<style lang="less" scoped>
.parameter-preview {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.preview-header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  h2 {
    margin: 0 0 10px 0;
    color: #333;
  }

  .preview-info {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #666;

    span {
      background: #f0f0f0;
      padding: 4px 8px;
      border-radius: 4px;
    }
  }
}

.preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.loading, .error {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.canvas-container {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: auto;
  margin-bottom: 20px;
}

.template-preview {
  padding: 20px;
  min-height: 100%;
}

.template-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.page {
  position: relative;
  margin-bottom: 20px;
  border: 1px solid #ddd;
  background: white;
}

.page-content {
  position: relative;
  width: 100%;
  height: 400px; // 默认高度
}

.text-element {
  border: 1px dashed #ccc;
  background: rgba(255,255,255,0.8);
  
  &:hover {
    border-color: #409eff;
    background: rgba(64, 158, 255, 0.1);
  }
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>
