
export type TZoomData = {
  text: string
  value: number
}

export const ZoomList: TZoomData[] = [
  {
    text: '25%',
    value: 25,
  },
  {
    text: '50%',
    value: 50,
  },
  {
    text: '75%',
    value: 75,
  },
  {
    text: '100%',
    value: 100,
  },
  {
    text: '125%',
    value: 125,
  },
  {
    text: '150%',
    value: 150,
  },
  {
    text: '200%',
    value: 200,
  },
  {
    text: '适应屏幕',
    value: -1,
    // icon: 'icon-best-size',
  },
]


export const OtherList: TZoomData[] = [
  {
    text: '250%',
    value: 250,
  },
  {
    text: '300%',
    value: 300,
  },
  {
    text: '350%',
    value: 350,
  },
  {
    text: '400%',
    value: 400,
  },
  {
    text: '450%',
    value: 450,
  },
  {
    text: '500%',
    value: 500,
  },
]
