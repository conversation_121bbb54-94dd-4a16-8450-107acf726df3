import { ExternalApiService } from '../services/externalApi'
import { ExternalApiConfig } from '../configs'

// Mock node-fetch
const mockFetch = jest.fn()
jest.mock('node-fetch', () => mockFetch)

describe('ExternalApiService', () => {
  let service: ExternalApiService
  let config: ExternalApiConfig

  beforeEach(() => {
    config = {
      baseUrl: 'http://localhost:3000/api',
      apiKey: 'test-api-key',
      timeout: 5000,
      retryAttempts: 3
    }
    service = new ExternalApiService(config)
    mockFetch.mockClear()
  })

  describe('getParameterData', () => {
    test('should successfully get parameter data', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          code: 200,
          data: {
            id: 'data-123',
            configId: 'config-456',
            templateId: '2',
            parameterValues: {
              greeting: '你好,新年快乐',
              quote: '成功不是终点'
            }
          }
        })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await service.getParameterData('data-123')

      expect(result).toEqual({
        id: 'data-123',
        configId: 'config-456',
        templateId: '2',
        parameterValues: {
          greeting: '你好,新年快乐',
          quote: '成功不是终点'
        }
      })
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/external/parameter-data/data-123',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-api-key'
          })
        })
      )
    })

    test('should use cache for repeated requests', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          code: 200,
          data: {
            id: 'data-123',
            configId: 'config-456',
            templateId: '2',
            parameterValues: {}
          }
        })
      }
      mockFetch.mockResolvedValue(mockResponse)

      // First request
      await service.getParameterData('data-123')
      // Second request (should use cache)
      await service.getParameterData('data-123')

      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    test('should handle API errors', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          code: 400,
          message: 'Data not found'
        })
      }
      mockFetch.mockResolvedValue(mockResponse)

      await expect(service.getParameterData('invalid-id')).rejects.toThrow('Failed to retrieve parameter data')
    })
  })

  describe('getParameterConfig', () => {
    test('should successfully get parameter config', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          code: 200,
          data: {
            id: 'config-456',
            templateId: '2',
            parameters: [
              {
                id: 'param-1',
                elementUuid: '98fd9b16db8a',
                parameterName: 'greeting',
                parameterLabel: '问候语',
                parameterType: 'text',
                isRequired: true,
                isEnabled: true,
                displayOrder: 1
              }
            ]
          }
        })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await service.getParameterConfig('config-456')

      expect(result.id).toBe('config-456')
      expect(result.templateId).toBe('2')
      expect(result.parameters).toHaveLength(1)
      expect(result.parameters[0].parameterName).toBe('greeting')
    })
  })

  describe('validateApiConnection', () => {
    test('should return true for successful health check', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          code: 200,
          data: {
            status: 'ok',
            timestamp: '2025-01-16T10:00:00Z'
          }
        })
      }
      mockFetch.mockResolvedValue(mockResponse)

      const result = await service.validateApiConnection()

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/external/health',
        expect.any(Object)
      )
    })

    test('should return false for failed health check', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      const result = await service.validateApiConnection()

      expect(result).toBe(false)
    })
  })

  describe('retry mechanism', () => {
    test('should retry on network failure', async () => {
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: jest.fn().mockResolvedValue({
            code: 200,
            data: {
              id: 'data-123',
              configId: 'config-456',
              templateId: '2',
              parameterValues: {}
            }
          })
        })

      const result = await service.getParameterData('data-123')

      expect(result.id).toBe('data-123')
      expect(mockFetch).toHaveBeenCalledTimes(3)
    })

    test('should fail after max retry attempts', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(service.getParameterData('data-123')).rejects.toThrow('Network error')
      expect(mockFetch).toHaveBeenCalledTimes(3) // Initial + 2 retries
    })
  })

  describe('HTTP status handling', () => {
    test('should handle HTTP error status', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found'
      }
      mockFetch.mockResolvedValue(mockResponse)

      await expect(service.getParameterData('data-123')).rejects.toThrow('HTTP 404: Not Found')
    })
  })
})
