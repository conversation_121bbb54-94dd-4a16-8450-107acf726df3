import { Router, Request, Response } from 'express'
import { ParameterController } from '../controllers/parameterController'

const router = Router()
const parameterController = new ParameterController()

// 模板解析
router.post('/api/template/parse', (req: Request, res: Response) => parameterController.parseTemplate(req, res))

// 参数化预览
router.post('/api/parameter/preview', (req: Request, res: Response) => parameterController.generatePreview(req, res))

// 预览页面
router.get('/preview/parameter/:dataId', (req: Request, res: Response) => parameterController.previewPage(req, res))

// 批量生成
router.post('/api/parameter/batch-generate', (req: Request, res: Response) => parameterController.batchGenerate(req, res))

// 批量状态查询
router.get('/api/parameter/batch-status/:batchId', (req: Request, res: Response) => parameterController.getBatchStatus(req, res))

export default router
