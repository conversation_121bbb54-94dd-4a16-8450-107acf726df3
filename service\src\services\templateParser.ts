import fs from 'fs'
import path from 'path'

// 类型定义
export enum ParameterType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  EMAIL = 'email',
  PHONE = 'phone',
  URL = 'url'
}

export enum TextCategory {
  PHONE = 'phone',
  ADDRESS = 'address',
  EMAIL = 'email',
  DATE = 'date',
  GENERAL = 'general'
}

export interface ElementPosition {
  left: number
  top: number
  width: number
  height: number
}

export interface ElementStyle {
  fontSize: number
  color: string
  fontFamily: string
  textAlign: string
  lineHeight: number
  letterSpacing: number
  fontWeight: string | number
  fontStyle: string
}

export interface TextElement {
  uuid: string
  type: 'w-text'
  text: string
  position: ElementPosition
  style: ElementStyle
}

export interface ParameterCandidate {
  elementUuid: string
  suggestedName: string
  suggestedLabel: string
  suggestedType: ParameterType
  originalText: string
  textCategory: TextCategory
}

export interface TemplateParseResult {
  templateId: string
  templateTitle: string
  textElements: TextElement[]
  parameterCandidates: ParameterCandidate[]
}

export class TemplateParserService {
  /**
   * 获取模板数据
   * @param templateId 模板ID
   * @returns 模板数据
   */
  async getTemplateData(templateId: string): Promise<any> {
    try {
      const templatePath = path.resolve(__dirname, `../mock/templates/${templateId}.json`)
      const templateData = fs.readFileSync(templatePath, 'utf8')
      return JSON.parse(templateData)
    } catch (error) {
      throw new Error(`Failed to load template ${templateId}: ${error.message}`)
    }
  }

  /**
   * 解析模板内容，生成参数候选项
   * @param templateId 模板ID
   * @returns 解析结果
   */
  async parseTemplate(templateId: string): Promise<TemplateParseResult> {
    // 1. 获取模板数据
    const templateData = await this.getTemplateData(templateId)

    // 2. 解析模板结构
    const parsedData = this.parseTemplateStructure(templateData)

    // 3. 提取文本元素
    const textElements = this.extractTextElements(parsedData)

    // 4. 生成参数候选项
    const parameterCandidates = this.generateParameterCandidates(textElements)

    return {
      templateId,
      templateTitle: templateData.title,
      textElements,
      parameterCandidates
    }
  }

  /**
   * 解析模板结构
   * @param templateData 模板数据
   * @returns 解析后的数据
   */
  private parseTemplateStructure(templateData: any): any {
    // 处理多页面模板格式
    let content = JSON.parse(templateData.data)

    if (Array.isArray(content)) {
      // 多页面模板，取第一页
      const { global, layers } = content[0]
      content = { page: global, widgets: layers }
    }

    return content
  }

  /**
   * 提取文本元素
   * @param templateData 模板数据
   * @returns 文本元素列表
   */
  private extractTextElements(templateData: any): TextElement[] {
    const textElements: TextElement[] = []
    const layers = templateData.widgets || templateData.layers || []

    layers.forEach((layer: any) => {
      if (layer.type === 'w-text' && layer.text) {
        textElements.push({
          uuid: layer.uuid,
          type: layer.type,
          text: layer.text,
          position: {
            left: layer.left,
            top: layer.top,
            width: layer.width,
            height: layer.height
          },
          style: {
            fontSize: layer.fontSize,
            color: layer.color,
            fontFamily: layer.fontFamily || layer.fontClass?.alias || 'default',
            textAlign: layer.textAlign,
            lineHeight: layer.lineHeight,
            letterSpacing: layer.letterSpacing,
            fontWeight: layer.fontWeight,
            fontStyle: layer.fontStyle
          }
        })
      }
    })

    return textElements
  }

  /**
   * 生成参数候选项
   * @param textElements 文本元素列表
   * @returns 参数候选项列表
   */
  private generateParameterCandidates(textElements: TextElement[]): ParameterCandidate[] {
    return textElements.map((element, index) => {
      const category = this.analyzeTextCategory(element.text)
      const suggestedName = this.suggestParameterName(element.text, category, index)
      const suggestedLabel = this.suggestParameterLabel(element.text, category)
      const suggestedType = this.suggestParameterType(category)

      return {
        elementUuid: element.uuid,
        suggestedName,
        suggestedLabel,
        suggestedType,
        originalText: element.text,
        textCategory: category
      }
    })
  }

  /**
   * 分析文本类别
   * @param text 文本内容
   * @returns 文本类别
   */
  private analyzeTextCategory(text: string): TextCategory {
    // 移除HTML标签进行分析
    const cleanText = text.replace(/<[^>]*>/g, '')

    if (/电话|手机|tel|phone/i.test(cleanText)) {
      return TextCategory.PHONE
    }
    if (/地址|address|addr/i.test(cleanText)) {
      return TextCategory.ADDRESS
    }
    if (/@.*\./i.test(cleanText)) {
      return TextCategory.EMAIL
    }
    if (/\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{1,2}月\d{1,2}日/i.test(cleanText)) {
      return TextCategory.DATE
    }

    return TextCategory.GENERAL
  }

  /**
   * 建议参数名称
   * @param text 文本内容
   * @param category 文本类别
   * @param index 索引
   * @returns 建议的参数名称
   */
  private suggestParameterName(text: string, category: TextCategory, index: number): string {
    const cleanText = text.replace(/<[^>]*>/g, '').toLowerCase()

    switch (category) {
      case TextCategory.PHONE:
        return 'phone'
      case TextCategory.ADDRESS:
        return 'address'
      case TextCategory.EMAIL:
        return 'email'
      case TextCategory.DATE:
        return 'date'
      default:
        // 根据文本内容生成参数名
        if (cleanText.includes('你好') || cleanText.includes('hello')) {
          return 'greeting'
        }
        if (cleanText.includes('名言') || cleanText.includes('quote') || cleanText.includes('马克思') || cleanText.includes('海洋')) {
          return 'quote'
        }
        if (cleanText.includes('标题') || cleanText.includes('title')) {
          return 'title'
        }
        if (cleanText.includes('每日一签') || cleanText.includes('#')) {
          return 'tag'
        }
        if (cleanText.includes('logo')) {
          return 'logo'
        }
        return `text_${index + 1}`
    }
  }

  /**
   * 建议参数标签
   * @param text 文本内容
   * @param category 文本类别
   * @returns 建议的参数标签
   */
  private suggestParameterLabel(text: string, category: TextCategory): string {
    const cleanText = text.replace(/<[^>]*>/g, '').toLowerCase()

    switch (category) {
      case TextCategory.PHONE:
        return '联系电话'
      case TextCategory.ADDRESS:
        return '详细地址'
      case TextCategory.EMAIL:
        return '邮箱地址'
      case TextCategory.DATE:
        return '日期时间'
      default:
        // 根据文本内容生成标签
        if (cleanText.includes('你好') || cleanText.includes('hello')) {
          return '问候语'
        }
        if (cleanText.includes('名言') || cleanText.includes('quote') || cleanText.includes('马克思') || cleanText.includes('海洋')) {
          return '励志名言'
        }
        if (cleanText.includes('每日一签') || cleanText.includes('#')) {
          return '标签文字'
        }
        if (cleanText.includes('logo')) {
          return 'Logo文字'
        }
        return '文本内容'
    }
  }

  /**
   * 建议参数类型
   * @param category 文本类别
   * @returns 建议的参数类型
   */
  private suggestParameterType(category: TextCategory): ParameterType {
    switch (category) {
      case TextCategory.PHONE:
        return ParameterType.PHONE
      case TextCategory.ADDRESS:
        return ParameterType.TEXTAREA
      case TextCategory.EMAIL:
        return ParameterType.EMAIL
      default:
        return ParameterType.TEXT
    }
  }
}
