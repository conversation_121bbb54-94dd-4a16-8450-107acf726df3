# 动态参数模板系统 API接口文档

## 迅排设计提供的API

### 1. 模板解析
**POST** `/api/template/parse`

解析模板内容，生成参数候选项。

**请求参数:**
```json
{
  "templateId": "2"
}
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "ok",
  "result": {
    "templateId": "2",
    "templateTitle": "示例模板",
    "textElements": [...],
    "parameterCandidates": [...]
  }
}
```

### 2. 参数化预览
**POST** `/api/parameter/preview`

生成参数化预览。

**请求参数:**
```json
{
  "templateId": "2",
  "parameterDataId": "user-data-123"
}
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "ok",
  "result": {
    "modifiedTemplateData": {...},
    "previewUrl": "/preview/parameter/user-data-123"
  }
}
```

### 3. 预览页面
**GET** `/preview/parameter/:dataId`

返回参数化预览页面HTML。

### 4. 参数化截图
**GET** `/api/screenshots?parameterDataId=xxx&width=800&height=600`

生成参数化图片。

### 5. 批量生成
**POST** `/api/parameter/batch-generate`

批量生成图片。

**请求参数:**
```json
{
  "templateId": "2",
  "parameterDataIds": ["user-data-123", "user-data-124"],
  "options": {
    "width": 800,
    "height": 600
  }
}
```

### 6. 批量状态查询
**GET** `/api/parameter/batch-status/:batchId`

查询批量任务状态。

## 主项目需要实现的API

### 1. 获取参数数据
**GET** `/api/external/parameter-data/:dataId`

**Headers:**
```
Authorization: Bearer your-api-key
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "id": "user-data-123",
    "configId": "config-456",
    "templateId": "2",
    "parameterValues": {
      "greeting": "你好,新年快乐",
      "quote": "成功不是终点..."
    }
  }
}
```

### 2. 获取参数配置
**GET** `/api/external/parameter-config/:configId`

**Headers:**
```
Authorization: Bearer your-api-key
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "id": "config-456",
    "templateId": "2",
    "parameters": [
      {
        "id": "param-1",
        "elementUuid": "98fd9b16db8a",
        "parameterName": "greeting",
        "parameterLabel": "问候语",
        "parameterType": "text",
        "isRequired": true,
        "isEnabled": true,
        "displayOrder": 1
      }
    ]
  }
}
```

### 3. 健康检查
**GET** `/api/external/health`

**Headers:**
```
Authorization: Bearer your-api-key
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "status": "ok",
    "timestamp": "2025-01-16T10:00:00Z"
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证说明

所有外部API调用都需要在请求头中包含API Key：
```
Authorization: Bearer your-secure-api-key
```

## 限制说明

- 批量生成最大支持50个项目
- API调用频率限制：100次/分钟
- 单次请求超时时间：30秒
