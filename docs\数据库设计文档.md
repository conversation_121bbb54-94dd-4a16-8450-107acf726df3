# 动态参数模板系统 - 数据库设计文档

## 概述

本文档详细说明动态参数模板系统的数据库设计，包括表结构、索引、关系设计等。

## 表结构设计

### 1. 参数配置表 (parameter_configs)

存储模板的参数配置信息。

```sql
CREATE TABLE parameter_configs (
  id VARCHAR(50) PRIMARY KEY COMMENT '配置ID',
  template_id VARCHAR(50) NOT NULL COMMENT '模板ID',
  template_title VARCHAR(200) DEFAULT '' COMMENT '模板标题',
  parameters JSON NOT NULL COMMENT '参数配置JSON',
  status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  created_by VARCHAR(50) DEFAULT '' COMMENT '创建者',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_template_id (template_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='参数配置表';
```

**parameters字段JSON结构:**
```json
[
  {
    "id": "param-1",
    "elementUuid": "98fd9b16db8a",
    "parameterName": "greeting",
    "parameterLabel": "问候语",
    "parameterType": "text",
    "isRequired": true,
    "defaultValue": "你好",
    "validationRules": {
      "minLength": 1,
      "maxLength": 50,
      "pattern": "",
      "errorMessage": "请输入1-50个字符"
    },
    "displayOrder": 1,
    "isEnabled": true
  }
]
```

### 2. 参数数据表 (parameter_data)

存储用户填写的参数数据。

```sql
CREATE TABLE parameter_data (
  id VARCHAR(50) PRIMARY KEY COMMENT '数据ID',
  config_id VARCHAR(50) NOT NULL COMMENT '配置ID',
  template_id VARCHAR(50) NOT NULL COMMENT '模板ID',
  parameter_values JSON NOT NULL COMMENT '参数值JSON',
  user_id VARCHAR(50) DEFAULT '' COMMENT '用户ID',
  status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_config_id (config_id),
  INDEX idx_template_id (template_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (config_id) REFERENCES parameter_configs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='参数数据表';
```

**parameter_values字段JSON结构:**
```json
{
  "greeting": "你好,新年快乐",
  "quote": "成功不是终点，失败不是致命的，重要的是继续前进的勇气。",
  "contact": "电话：138-0000-0000\n地址：北京市朝阳区xxx路123号"
}
```

### 3. 批量任务表 (batch_tasks)

存储批量生成任务信息。

```sql
CREATE TABLE batch_tasks (
  id VARCHAR(50) PRIMARY KEY COMMENT '任务ID',
  template_id VARCHAR(50) NOT NULL COMMENT '模板ID',
  total_count INT DEFAULT 0 COMMENT '总数量',
  completed_count INT DEFAULT 0 COMMENT '完成数量',
  failed_count INT DEFAULT 0 COMMENT '失败数量',
  status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '任务状态',
  options JSON DEFAULT NULL COMMENT '生成选项',
  results JSON DEFAULT NULL COMMENT '生成结果',
  user_id VARCHAR(50) DEFAULT '' COMMENT '用户ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_template_id (template_id),
  INDEX idx_status (status),
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批量任务表';
```

### 4. 批量任务项表 (batch_task_items)

存储批量任务的具体项目。

```sql
CREATE TABLE batch_task_items (
  id VARCHAR(50) PRIMARY KEY COMMENT '项目ID',
  batch_id VARCHAR(50) NOT NULL COMMENT '批量任务ID',
  parameter_data_id VARCHAR(50) NOT NULL COMMENT '参数数据ID',
  status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '项目状态',
  result_url VARCHAR(500) DEFAULT '' COMMENT '结果URL',
  error_message TEXT DEFAULT NULL COMMENT '错误信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_batch_id (batch_id),
  INDEX idx_parameter_data_id (parameter_data_id),
  INDEX idx_status (status),
  
  FOREIGN KEY (batch_id) REFERENCES batch_tasks(id) ON DELETE CASCADE,
  FOREIGN KEY (parameter_data_id) REFERENCES parameter_data(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批量任务项表';
```

## 索引设计

### 1. 主键索引
所有表都使用VARCHAR(50)作为主键，确保全局唯一性。

### 2. 外键索引
- parameter_data.config_id → parameter_configs.id
- batch_task_items.batch_id → batch_tasks.id
- batch_task_items.parameter_data_id → parameter_data.id

### 3. 查询索引
- template_id: 按模板查询配置和数据
- user_id: 按用户查询数据
- status: 按状态过滤
- created_at: 按时间排序

### 4. 复合索引
```sql
-- 用户模板数据查询
ALTER TABLE parameter_data ADD INDEX idx_user_template (user_id, template_id, status);

-- 批量任务状态查询
ALTER TABLE batch_task_items ADD INDEX idx_batch_status (batch_id, status);
```

## 数据关系图

```
parameter_configs (1) ──→ (N) parameter_data
                                    │
                                    │ (N)
                                    ↓
batch_tasks (1) ──→ (N) batch_task_items
```

## 数据初始化

### 1. 示例配置数据
```sql
INSERT INTO parameter_configs (id, template_id, template_title, parameters) VALUES 
('config-456', '2', '示例模板 - 日签插画手机海报', '[
  {
    "id": "param-1",
    "elementUuid": "98fd9b16db8a",
    "parameterName": "greeting",
    "parameterLabel": "个性问候语",
    "parameterType": "text",
    "isRequired": true,
    "defaultValue": "你好,十二月",
    "isEnabled": true,
    "displayOrder": 1
  }
]');
```

### 2. 示例参数数据
```sql
INSERT INTO parameter_data (id, config_id, template_id, parameter_values) VALUES 
('user-data-123', 'config-456', '2', '{
  "greeting": "你好,新年快乐",
  "quote": "成功不是终点，失败不是致命的，重要的是继续前进的勇气。——丘吉尔"
}');
```

## 性能优化建议

### 1. 分区策略
对于大数据量场景，可以按时间分区：
```sql
-- 按月分区
ALTER TABLE parameter_data PARTITION BY RANGE (YEAR(created_at)*100 + MONTH(created_at)) (
  PARTITION p202501 VALUES LESS THAN (202502),
  PARTITION p202502 VALUES LESS THAN (202503),
  -- ...
);
```

### 2. 缓存策略
- 参数配置数据使用Redis缓存，TTL设置为1小时
- 热点参数数据缓存，TTL设置为30分钟

### 3. 读写分离
- 读操作使用从库
- 写操作使用主库
- 批量任务使用专用数据库

## 备份和恢复

### 1. 备份策略
- 全量备份：每日凌晨2点
- 增量备份：每4小时一次
- 日志备份：实时

### 2. 恢复测试
- 每月进行一次恢复测试
- 验证数据完整性和一致性

## 监控指标

### 1. 性能指标
- 查询响应时间
- 并发连接数
- 慢查询统计

### 2. 业务指标
- 参数配置数量
- 用户数据量
- 批量任务成功率
