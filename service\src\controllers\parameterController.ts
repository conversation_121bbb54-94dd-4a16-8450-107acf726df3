import { TemplateParserService } from '../services/templateParser'
import { ParameterEngineService } from '../services/parameterEngine'
import { ExternalApiService } from '../services/externalApi'
import { externalApiConfig } from '../configs'
import { send } from '../utils/tools'

export class ParameterController {
  private templateParser: TemplateParserService
  private parameterEngine: ParameterEngineService
  private externalApi: ExternalApiService

  constructor() {
    this.templateParser = new TemplateParserService()
    this.externalApi = new ExternalApiService(externalApiConfig)
    this.parameterEngine = new ParameterEngineService(this.externalApi)
  }

  /**
   * @api {POST} /api/template/parse 解析模板内容
   * @apiName ParseTemplate
   * @apiGroup Template
   *
   * @apiParam {String} templateId 模板ID
   *
   * @apiSuccess {Object} data 解析结果
   * @apiSuccess {String} data.templateId 模板ID
   * @apiSuccess {String} data.templateTitle 模板标题
   * @apiSuccess {Array} data.textElements 文本元素列表
   * @apiSuccess {Array} data.parameterCandidates 参数候选项列表
   */
  async parseTemplate(req: any, res: any) {
    try {
      const { templateId } = req.body

      if (!templateId) {
        return send.error(res, 'Template ID is required')
      }

      const result = await this.templateParser.parseTemplate(templateId)
      send.success(res, result)
    } catch (error) {
      console.error('Parse template error:', error)
      send.error(res, 'Failed to parse template')
    }
  }

  /**
   * @api {POST} /api/parameter/preview 生成参数化预览
   * @apiName GeneratePreview
   * @apiGroup Parameter
   *
   * @apiParam {String} templateId 模板ID
   * @apiParam {String} parameterDataId 参数数据ID
   *
   * @apiSuccess {Object} data 预览结果
   * @apiSuccess {Object} data.modifiedTemplateData 修改后的模板数据
   * @apiSuccess {String} data.previewUrl 预览URL
   */
  async generatePreview(req: any, res: any) {
    try {
      const { templateId, parameterDataId } = req.body

      if (!templateId || !parameterDataId) {
        return send.error(res, 'Template ID and parameter data ID are required')
      }

      const result = await this.parameterEngine.replaceContent({
        templateId,
        parameterDataId
      })

      send.success(res, result)
    } catch (error) {
      console.error('Generate preview error:', error)
      send.error(res, 'Failed to generate preview')
    }
  }

  /**
   * @api {GET} /preview/parameter/:dataId 参数化预览页面
   * @apiName PreviewPage
   * @apiGroup Parameter
   *
   * @apiParam {String} dataId 参数数据ID
   *
   * @apiSuccess {String} html 预览页面HTML
   */
  async previewPage(req: any, res: any) {
    try {
      const { dataId } = req.params

      if (!dataId) {
        return res.status(400).send('Parameter data ID is required')
      }

      // 获取参数数据
      const parameterData = await this.externalApi.getParameterData(dataId)

      // 生成预览HTML
      const html = await this.generatePreviewHtml(parameterData.templateId, dataId)

      res.setHeader('Content-Type', 'text/html')
      res.send(html)
    } catch (error) {
      console.error('Preview page error:', error)
      res.status(500).send('Failed to generate preview page')
    }
  }

  /**
   * 生成预览HTML
   * @param templateId 模板ID
   * @param parameterDataId 参数数据ID
   * @returns HTML内容
   */
  private async generatePreviewHtml(templateId: string, parameterDataId: string): Promise<string> {
    // 获取替换后的模板数据
    const result = await this.parameterEngine.replaceContent({
      templateId,
      parameterDataId
    })

    // 生成预览页面HTML
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数化预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .preview-header {
            background: #333;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
        }
        .preview-content {
            padding: 20px;
        }
        .template-data {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            参数化模板预览 - 模板ID: ${templateId}
        </div>
        <div class="preview-content">
            <h3>替换后的模板数据：</h3>
            <div class="template-data">${JSON.stringify(result.modifiedTemplateData, null, 2)}</div>
        </div>
    </div>
    <script>
        // 可以在这里添加JavaScript代码来渲染实际的设计预览
        console.log('Preview data:', ${JSON.stringify(result.modifiedTemplateData)});
    </script>
</body>
</html>`

    return html
  }

  /**
   * @api {POST} /api/parameter/batch-generate 批量生成图片
   * @apiName BatchGenerate
   * @apiGroup Parameter
   *
   * @apiParam {String} templateId 模板ID
   * @apiParam {Array} parameterDataIds 参数数据ID数组
   * @apiParam {Object} options 生成选项
   *
   * @apiSuccess {Object} data 批量任务信息
   * @apiSuccess {String} data.batchId 批量任务ID
   * @apiSuccess {Number} data.total 总数量
   * @apiSuccess {String} data.status 任务状态
   */
  async batchGenerate(req: any, res: any) {
    try {
      const { templateId, parameterDataIds, options = {} } = req.body

      if (!templateId || !parameterDataIds || !Array.isArray(parameterDataIds)) {
        return send.error(res, 'Template ID and parameter data IDs array are required')
      }

      if (parameterDataIds.length > 50) {
        return send.error(res, 'Batch size cannot exceed 50')
      }

      const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // 启动批量处理（异步）
      this.processBatchGeneration(batchId, templateId, parameterDataIds, options)

      send.success(res, {
        batchId,
        total: parameterDataIds.length,
        status: 'processing'
      })
    } catch (error) {
      console.error('Batch generate error:', error)
      send.error(res, 'Failed to start batch generation')
    }
  }

  /**
   * @api {GET} /api/parameter/batch-status/:batchId 查询批量状态
   * @apiName GetBatchStatus
   * @apiGroup Parameter
   *
   * @apiParam {String} batchId 批量任务ID
   *
   * @apiSuccess {Object} data 批量状态信息
   */
  async getBatchStatus(req: any, res: any) {
    try {
      const { batchId } = req.params

      if (!batchId) {
        return send.error(res, 'Batch ID is required')
      }

      // 这里应该从数据库或缓存中获取状态
      // 简化实现，返回模拟状态
      const status = {
        batchId,
        status: 'completed',
        total: 3,
        completed: 3,
        failed: 0,
        results: [
          { dataId: 'user-data-123', status: 'success', imageUrl: '/cache/user-data-123-screenshot.png' }
        ]
      }

      send.success(res, status)
    } catch (error) {
      console.error('Get batch status error:', error)
      send.error(res, 'Failed to get batch status')
    }
  }

  /**
   * 处理批量生成
   * @param batchId 批量ID
   * @param templateId 模板ID
   * @param parameterDataIds 参数数据ID数组
   * @param options 选项
   */
  private async processBatchGeneration(batchId: string, templateId: string, parameterDataIds: string[], options: any) {
    console.log(`[BatchGeneration] Starting batch ${batchId} with ${parameterDataIds.length} items`)

    for (const dataId of parameterDataIds) {
      try {
        await this.generateSingleImage(templateId, dataId, options)
        console.log(`[BatchGeneration] Completed ${dataId}`)
      } catch (error) {
        console.error(`[BatchGeneration] Failed ${dataId}:`, error)
      }
    }

    console.log(`[BatchGeneration] Batch ${batchId} completed`)
  }

  /**
   * 生成单个图片
   * @param templateId 模板ID
   * @param parameterDataId 参数数据ID
   * @param options 选项
   */
  private async generateSingleImage(templateId: string, parameterDataId: string, options: any) {
    // 这里应该调用截图服务
    // 简化实现，模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log(`Generated image for ${parameterDataId}`)
  }
}
