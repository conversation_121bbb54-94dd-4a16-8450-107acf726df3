# 动态参数模板系统API测试脚本
# 作者：AI Assistant
# 日期：2025-08-17

Write-Host "=== 动态参数模板系统API测试开始 ===" -ForegroundColor Green
Write-Host ""

# 测试结果记录
$testResults = @()

# 辅助函数：记录测试结果
function Record-TestResult {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Url,
        [int]$StatusCode,
        [string]$Response,
        [bool]$Passed,
        [string]$ErrorMessage = ""
    )
    
    $result = @{
        TestName = $TestName
        Method = $Method
        Url = $Url
        StatusCode = $StatusCode
        Response = $Response
        Passed = $Passed
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    $color = if ($Passed) { "Green" } else { "Red" }
    
    Write-Host "[$status] $TestName" -ForegroundColor $color
    if (-not $Passed -and $ErrorMessage) {
        Write-Host "  Error: $ErrorMessage" -ForegroundColor Red
    }
    $responsePreview = if ($Response -and $Response.Length -gt 0) {
        $Response.Substring(0, [Math]::Min(100, $Response.Length))
    } else {
        "Empty response"
    }
    Write-Host "  Status: $StatusCode | Response: $responsePreview..." -ForegroundColor Gray
    Write-Host ""
}

# 辅助函数：执行HTTP请求
function Invoke-ApiTest {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Url,
        [hashtable]$Headers = @{},
        [string]$Body = "",
        [int]$ExpectedStatus = 200
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
        }
        
        if ($Body -and $Method -in @("POST", "PUT", "PATCH")) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        $passed = $response.StatusCode -eq $ExpectedStatus
        
        Record-TestResult -TestName $TestName -Method $Method -Url $Url -StatusCode $response.StatusCode -Response $response.Content -Passed $passed
        
        return $response
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        Record-TestResult -TestName $TestName -Method $Method -Url $Url -StatusCode $statusCode -Response $_.Exception.Message -Passed $false -ErrorMessage $_.Exception.Message
        return $null
    }
}

Write-Host "1. 测试模拟API服务器" -ForegroundColor Yellow
Write-Host "================================"

# 测试1.1：健康检查 - 正常情况
Invoke-ApiTest -TestName "健康检查-正常" -Method "GET" -Url "http://localhost:3000/api/external/health" -Headers @{"Authorization"="Bearer test-api-key"}

# 测试1.2：健康检查 - 无效API Key
Invoke-ApiTest -TestName "健康检查-无效API Key" -Method "GET" -Url "http://localhost:3000/api/external/health" -Headers @{"Authorization"="Bearer invalid-key"} -ExpectedStatus 401

# 测试1.3：健康检查 - 缺失API Key
Invoke-ApiTest -TestName "健康检查-缺失API Key" -Method "GET" -Url "http://localhost:3000/api/external/health" -ExpectedStatus 401

# 测试1.4：获取参数数据 - 正常情况
Invoke-ApiTest -TestName "获取参数数据-正常" -Method "GET" -Url "http://localhost:3000/api/external/parameter-data/user-data-123" -Headers @{"Authorization"="Bearer test-api-key"}

# 测试1.5：获取参数数据 - 不存在的数据
Invoke-ApiTest -TestName "获取参数数据-不存在" -Method "GET" -Url "http://localhost:3000/api/external/parameter-data/invalid-data" -Headers @{"Authorization"="Bearer test-api-key"} -ExpectedStatus 404

# 测试1.6：获取参数配置 - 正常情况
Invoke-ApiTest -TestName "获取参数配置-正常" -Method "GET" -Url "http://localhost:3000/api/external/parameter-config/config-456" -Headers @{"Authorization"="Bearer test-api-key"}

# 测试1.7：获取参数配置 - 不存在的配置
Invoke-ApiTest -TestName "获取参数配置-不存在" -Method "GET" -Url "http://localhost:3000/api/external/parameter-config/invalid-config" -Headers @{"Authorization"="Bearer test-api-key"} -ExpectedStatus 404

Write-Host "2. 测试迅排设计API" -ForegroundColor Yellow
Write-Host "================================"

# 测试2.1：模板解析 - 正常情况
$parseBody = '{"templateId": "2"}'
Invoke-ApiTest -TestName "模板解析-正常" -Method "POST" -Url "http://localhost:7001/api/template/parse" -Body $parseBody

# 测试2.2：模板解析 - 缺失参数
$parseBodyEmpty = '{}'
Invoke-ApiTest -TestName "模板解析-缺失参数" -Method "POST" -Url "http://localhost:7001/api/template/parse" -Body $parseBodyEmpty -ExpectedStatus 400

# 测试2.3：模板解析 - 无效模板ID
$parseBodyInvalid = '{"templateId": "999"}'
Invoke-ApiTest -TestName "模板解析-无效模板ID" -Method "POST" -Url "http://localhost:7001/api/template/parse" -Body $parseBodyInvalid -ExpectedStatus 500

# 测试2.4：参数化预览 - 正常情况
$previewBody = '{"templateId": "2", "parameterDataId": "user-data-123"}'
Invoke-ApiTest -TestName "参数化预览-正常" -Method "POST" -Url "http://localhost:7001/api/parameter/preview" -Body $previewBody

# 测试2.5：参数化预览 - 缺失参数
$previewBodyEmpty = '{"templateId": "2"}'
Invoke-ApiTest -TestName "参数化预览-缺失参数" -Method "POST" -Url "http://localhost:7001/api/parameter/preview" -Body $previewBodyEmpty -ExpectedStatus 400

# 测试2.6：预览页面 - 正常情况
Invoke-ApiTest -TestName "预览页面-正常" -Method "GET" -Url "http://localhost:7001/preview/parameter/user-data-123"

# 测试2.7：预览页面 - 无效数据ID
Invoke-ApiTest -TestName "预览页面-无效数据ID" -Method "GET" -Url "http://localhost:7001/preview/parameter/invalid-data" -ExpectedStatus 500

Write-Host "3. 测试结果汇总" -ForegroundColor Yellow
Write-Host "================================"

$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Passed }).Count
$failedTests = $totalTests - $passedTests
$passRate = [math]::Round(($passedTests / $totalTests) * 100, 2)

Write-Host "总测试数: $totalTests" -ForegroundColor White
Write-Host "通过数: $passedTests" -ForegroundColor Green
Write-Host "失败数: $failedTests" -ForegroundColor Red
Write-Host "通过率: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } else { "Red" })

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
