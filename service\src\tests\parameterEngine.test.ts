import { ParameterEngineService } from '../services/parameterEngine'
import { ExternalApiService } from '../services/externalApi'

// Mock ExternalApiService
const mockExternalApi = {
  getParameterData: jest.fn(),
  getParameterConfig: jest.fn(),
  validateApiConnection: jest.fn()
} as jest.Mocked<ExternalApiService>

// Mock fs
jest.mock('fs', () => ({
  readFileSync: jest.fn()
}))

const fs = require('fs')

describe('ParameterEngineService', () => {
  let service: ParameterEngineService

  beforeEach(() => {
    service = new ParameterEngineService(mockExternalApi)
    jest.clearAllMocks()
  })

  describe('replaceContent', () => {
    const mockTemplateData = {
      title: '示例模板',
      data: JSON.stringify({
        layers: [
          {
            uuid: '98fd9b16db8a',
            type: 'w-text',
            text: '你好,十二月',
            left: 100,
            top: 50,
            width: 200,
            height: 40,
            fontSize: 24,
            color: '#333333'
          },
          {
            uuid: 'b12ffac161d3',
            type: 'w-text',
            text: '生活就像海洋，只有意志坚强的人，才能到达彼岸。——马克思',
            left: 50,
            top: 150,
            width: 300,
            height: 80,
            fontSize: 16,
            color: '#666666'
          }
        ]
      })
    }

    const mockParameterData = {
      id: 'data-123',
      configId: 'config-456',
      templateId: '2',
      parameterValues: {
        greeting: '你好,新年快乐',
        quote: '成功不是终点，失败不是致命的，重要的是继续前进的勇气。——丘吉尔'
      }
    }

    const mockParameterConfig = {
      id: 'config-456',
      templateId: '2',
      parameters: [
        {
          id: 'param-1',
          elementUuid: '98fd9b16db8a',
          parameterName: 'greeting',
          parameterLabel: '问候语',
          parameterType: 'text',
          isRequired: true,
          isEnabled: true,
          displayOrder: 1
        },
        {
          id: 'param-2',
          elementUuid: 'b12ffac161d3',
          parameterName: 'quote',
          parameterLabel: '励志名言',
          parameterType: 'textarea',
          isRequired: true,
          isEnabled: true,
          displayOrder: 2
        }
      ]
    }

    beforeEach(() => {
      fs.readFileSync.mockReturnValue(JSON.stringify(mockTemplateData))
      mockExternalApi.getParameterData.mockResolvedValue(mockParameterData)
      mockExternalApi.getParameterConfig.mockResolvedValue(mockParameterConfig)
    })

    test('should successfully replace content', async () => {
      const request = {
        templateId: '2',
        parameterDataId: 'data-123'
      }

      const result = await service.replaceContent(request)

      expect(result).toBeDefined()
      expect(result.modifiedTemplateData).toBeDefined()
      expect(result.previewUrl).toBe('/preview/parameter/data-123')

      // 验证模板数据被正确修改
      const modifiedData = JSON.parse(result.modifiedTemplateData.data)
      const layers = modifiedData.layers

      // 验证第一个文本元素被替换
      const greetingElement = layers.find((l: any) => l.uuid === '98fd9b16db8a')
      expect(greetingElement.text).toBe('你好,新年快乐')

      // 验证第二个文本元素被替换
      const quoteElement = layers.find((l: any) => l.uuid === 'b12ffac161d3')
      expect(quoteElement.text).toBe('成功不是终点，失败不是致命的，重要的是继续前进的勇气。——丘吉尔')
    })

    test('should preserve original styles after replacement', async () => {
      const request = {
        templateId: '2',
        parameterDataId: 'data-123'
      }

      const result = await service.replaceContent(request)
      const modifiedData = JSON.parse(result.modifiedTemplateData.data)
      const layers = modifiedData.layers

      // 验证样式属性保持不变
      const greetingElement = layers.find((l: any) => l.uuid === '98fd9b16db8a')
      expect(greetingElement.fontSize).toBe(24)
      expect(greetingElement.color).toBe('#333333')
      expect(greetingElement.left).toBe(100)
      expect(greetingElement.top).toBe(50)
    })

    test('should handle multi-page templates', async () => {
      const multiPageTemplate = {
        title: '多页面模板',
        data: JSON.stringify([
          {
            global: { width: 800, height: 600 },
            layers: [
              {
                uuid: '98fd9b16db8a',
                type: 'w-text',
                text: '你好,十二月',
                fontSize: 24
              }
            ]
          }
        ])
      }

      fs.readFileSync.mockReturnValue(JSON.stringify(multiPageTemplate))

      const request = {
        templateId: '2',
        parameterDataId: 'data-123'
      }

      const result = await service.replaceContent(request)
      const modifiedData = JSON.parse(result.modifiedTemplateData.data)

      expect(Array.isArray(modifiedData)).toBe(true)
      expect(modifiedData[0].layers[0].text).toBe('你好,新年快乐')
    })

    test('should handle HTML formatting in text', async () => {
      const htmlTemplate = {
        title: '带HTML格式的模板',
        data: JSON.stringify({
          layers: [
            {
              uuid: '98fd9b16db8a',
              type: 'w-text',
              text: '你好,<br/>十二月',
              fontSize: 24
            }
          ]
        })
      }

      const parameterDataWithNewlines = {
        ...mockParameterData,
        parameterValues: {
          greeting: '你好,\n新年快乐'
        }
      }

      fs.readFileSync.mockReturnValue(JSON.stringify(htmlTemplate))
      mockExternalApi.getParameterData.mockResolvedValue(parameterDataWithNewlines)

      const request = {
        templateId: '2',
        parameterDataId: 'data-123'
      }

      const result = await service.replaceContent(request)
      const modifiedData = JSON.parse(result.modifiedTemplateData.data)
      const textElement = modifiedData.layers[0]

      expect(textElement.text).toBe('你好,<br/>新年快乐')
    })

    test('should skip disabled parameters', async () => {
      const configWithDisabledParam = {
        ...mockParameterConfig,
        parameters: [
          {
            ...mockParameterConfig.parameters[0],
            isEnabled: false
          },
          mockParameterConfig.parameters[1]
        ]
      }

      mockExternalApi.getParameterConfig.mockResolvedValue(configWithDisabledParam)

      const request = {
        templateId: '2',
        parameterDataId: 'data-123'
      }

      const result = await service.replaceContent(request)
      const modifiedData = JSON.parse(result.modifiedTemplateData.data)
      const layers = modifiedData.layers

      // 第一个参数被禁用，应该保持原文本
      const greetingElement = layers.find((l: any) => l.uuid === '98fd9b16db8a')
      expect(greetingElement.text).toBe('你好,十二月')

      // 第二个参数启用，应该被替换
      const quoteElement = layers.find((l: any) => l.uuid === 'b12ffac161d3')
      expect(quoteElement.text).toBe('成功不是终点，失败不是致命的，重要的是继续前进的勇气。——丘吉尔')
    })

    test('should handle missing parameter values', async () => {
      const incompleteParameterData = {
        ...mockParameterData,
        parameterValues: {
          greeting: '你好,新年快乐'
          // quote 参数缺失
        }
      }

      mockExternalApi.getParameterData.mockResolvedValue(incompleteParameterData)

      const request = {
        templateId: '2',
        parameterDataId: 'data-123'
      }

      const result = await service.replaceContent(request)
      const modifiedData = JSON.parse(result.modifiedTemplateData.data)
      const layers = modifiedData.layers

      // 有值的参数应该被替换
      const greetingElement = layers.find((l: any) => l.uuid === '98fd9b16db8a')
      expect(greetingElement.text).toBe('你好,新年快乐')

      // 缺失的参数应该保持原文本
      const quoteElement = layers.find((l: any) => l.uuid === 'b12ffac161d3')
      expect(quoteElement.text).toBe('生活就像海洋，只有意志坚强的人，才能到达彼岸。——马克思')
    })

    test('should handle API errors gracefully', async () => {
      mockExternalApi.getParameterData.mockRejectedValue(new Error('API Error'))

      const request = {
        templateId: '2',
        parameterDataId: 'data-123'
      }

      await expect(service.replaceContent(request)).rejects.toThrow('Failed to replace content')
    })

    test('should handle template loading errors', async () => {
      fs.readFileSync.mockImplementation(() => {
        throw new Error('File not found')
      })

      const request = {
        templateId: 'invalid',
        parameterDataId: 'data-123'
      }

      await expect(service.replaceContent(request)).rejects.toThrow('Failed to replace content')
    })
  })
})
