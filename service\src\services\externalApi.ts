import { ExternalApiConfig } from '../configs'

// 类型定义
export interface ParameterData {
  id: string
  configId: string
  parameterValues: Record<string, any>
  templateId: string
}

export interface ParameterDefinition {
  id: string
  elementUuid: string
  parameterName: string
  parameterLabel: string
  parameterType: string
  isRequired: boolean
  defaultValue?: string
  validationRules?: any
  displayOrder: number
  isEnabled: boolean
}

export interface ParameterConfig {
  id: string
  templateId: string
  parameters: ParameterDefinition[]
}

export interface ExternalParameterDataResponse {
  code: number
  data: {
    id: string
    configId: string
    templateId: string
    parameterValues: Record<string, any>
  }
}

export interface ExternalParameterConfigResponse {
  code: number
  data: {
    id: string
    templateId: string
    parameters: ParameterDefinition[]
  }
}

export interface ExternalHealthResponse {
  code: number
  data: {
    status: string
    timestamp: string
  }
}

interface RequestOptions {
  method?: string
  headers?: Record<string, string>
  body?: any
}

export class ExternalApiService {
  private config: ExternalApiConfig
  private cache: Map<string, any> = new Map()

  constructor(config: ExternalApiConfig) {
    this.config = config
  }

  /**
   * 获取参数数据
   * @param dataId 数据ID
   * @returns 参数数据
   */
  async getParameterData(dataId: string): Promise<ParameterData> {
    console.log(`[ExternalApiService] Getting parameter data for ID: ${dataId}`)

    const cacheKey = `parameter_data_${dataId}`

    if (this.cache.has(cacheKey)) {
      console.log(`[ExternalApiService] Cache hit for parameter data: ${dataId}`)
      return this.cache.get(cacheKey)
    }

    try {
      const response = await this.makeRequest<ExternalParameterDataResponse>(`/external/parameter-data/${dataId}`)
      const data: ParameterData = {
        id: response.data.id,
        configId: response.data.configId,
        templateId: response.data.templateId,
        parameterValues: response.data.parameterValues
      }

      // 缓存数据
      this.cache.set(cacheKey, data)
      setTimeout(() => this.cache.delete(cacheKey), this.config.timeout)

      console.log(`[ExternalApiService] Successfully retrieved parameter data: ${dataId}`)
      return data
    } catch (error) {
      console.error(`[ExternalApiService] Failed to get parameter data ${dataId}:`, error)
      throw new Error(`Failed to retrieve parameter data: ${error.message}`)
    }
  }

  /**
   * 获取参数配置
   * @param configId 配置ID
   * @returns 参数配置
   */
  async getParameterConfig(configId: string): Promise<ParameterConfig> {
    console.log(`[ExternalApiService] Getting parameter config for ID: ${configId}`)

    const cacheKey = `parameter_config_${configId}`

    if (this.cache.has(cacheKey)) {
      console.log(`[ExternalApiService] Cache hit for parameter config: ${configId}`)
      return this.cache.get(cacheKey)
    }

    try {
      const response = await this.makeRequest<ExternalParameterConfigResponse>(`/external/parameter-config/${configId}`)
      const config: ParameterConfig = {
        id: response.data.id,
        templateId: response.data.templateId,
        parameters: response.data.parameters
      }

      // 缓存配置
      this.cache.set(cacheKey, config)
      setTimeout(() => this.cache.delete(cacheKey), this.config.timeout * 2) // 配置缓存时间更长

      console.log(`[ExternalApiService] Successfully retrieved parameter config: ${configId}`)
      return config
    } catch (error) {
      console.error(`[ExternalApiService] Failed to get parameter config ${configId}:`, error)
      throw new Error(`Failed to retrieve parameter config: ${error.message}`)
    }
  }

  /**
   * 验证API连接
   * @returns 连接状态
   */
  async validateApiConnection(): Promise<boolean> {
    console.log(`[ExternalApiService] Validating API connection to: ${this.config.baseUrl}`)

    try {
      await this.makeRequest<ExternalHealthResponse>('/external/health')
      console.log(`[ExternalApiService] API connection validated successfully`)
      return true
    } catch (error) {
      console.error(`[ExternalApiService] API connection validation failed:`, error)
      return false
    }
  }

  /**
   * 发起HTTP请求
   * @param endpoint 端点
   * @param options 请求选项
   * @returns 响应数据
   */
  private async makeRequest<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`
    const requestOptions: any = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        ...options.headers
      },
      timeout: this.config.timeout,
      ...options
    }

    if (options.body) {
      requestOptions.body = JSON.stringify(options.body)
    }

    let lastError: Error = new Error('Unknown error')

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        console.log(`[ExternalApiService] Making request (attempt ${attempt}/${this.config.retryAttempts}): ${requestOptions.method} ${url}`)

        // 使用Node.js的fetch或axios
        const fetch = require('node-fetch')
        const response = await fetch(url, requestOptions)

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()

        if (result.code !== 200) {
          throw new Error(`API Error: ${result.message || 'Unknown error'}`)
        }

        console.log(`[ExternalApiService] Request successful: ${requestOptions.method} ${url}`)
        return result
      } catch (error) {
        lastError = error as Error
        console.error(`[ExternalApiService] Request failed (attempt ${attempt}/${this.config.retryAttempts}):`, error.message)

        if (attempt < this.config.retryAttempts) {
          // 指数退避重试
          const delay = Math.pow(2, attempt - 1) * 1000
          console.log(`[ExternalApiService] Retrying in ${delay}ms...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw lastError
  }
}
