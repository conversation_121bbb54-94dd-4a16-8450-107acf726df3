import express from 'express'
import cors from 'cors'

const app = express()
const PORT = 3000

app.use(cors())
app.use(express.json())

// 模拟数据
const mockParameterData: Record<string, any> = {
  'user-data-123': {
    id: 'user-data-123',
    configId: 'config-456',
    templateId: '2',
    parameterValues: {
      greeting: '你好,新年快乐',
      quote: '成功不是终点，失败不是致命的，重要的是继续前进的勇气。——丘吉尔',
      contact: '电话：138-0000-0000\n地址：北京市朝阳区xxx路123号'
    }
  }
}

const mockParameterConfig: Record<string, any> = {
  'config-456': {
    id: 'config-456',
    templateId: '2',
    parameters: [
      {
        id: 'param-1',
        elementUuid: '98fd9b16db8a',
        parameterName: 'greeting',
        parameterLabel: '个性问候语',
        parameterType: 'text',
        isRequired: true,
        defaultValue: '你好,十二月',
        isEnabled: true,
        displayOrder: 1
      },
      {
        id: 'param-2',
        elementUuid: 'b12ffac161d3',
        parameterName: 'quote',
        parameterLabel: '励志名言',
        parameterType: 'textarea',
        isRequired: true,
        defaultValue: '生活就像海洋，只有意志坚强的人，才能到达彼岸。——马克思',
        isEnabled: true,
        displayOrder: 2
      },
      {
        id: 'param-3',
        elementUuid: '764a25774e74',
        parameterName: 'contact',
        parameterLabel: '联系方式',
        parameterType: 'textarea',
        isRequired: false,
        defaultValue: '电话：8888-8888888\n地址：广州市高林路888号',
        isEnabled: true,
        displayOrder: 3
      }
    ]
  }
}

// 中间件：验证API Key
function validateApiKey(req: any, res: any, next: any) {
  const apiKey = req.headers.authorization?.replace('Bearer ', '')

  if (!apiKey || apiKey !== 'test-api-key') {
    return res.status(401).json({
      code: 401,
      message: 'Invalid API key'
    })
  }

  next()
}

// 获取参数数据
app.get('/api/external/parameter-data/:dataId', validateApiKey, (req: any, res: any) => {
  const { dataId } = req.params

  console.log(`[MockAPI] Getting parameter data for ID: ${dataId}`)

  const data = mockParameterData[dataId]
  if (!data) {
    return res.status(404).json({
      code: 404,
      message: 'Parameter data not found'
    })
  }

  res.json({
    code: 200,
    data
  })
})

// 获取参数配置
app.get('/api/external/parameter-config/:configId', validateApiKey, (req: any, res: any) => {
  const { configId } = req.params

  console.log(`[MockAPI] Getting parameter config for ID: ${configId}`)

  const config = mockParameterConfig[configId]
  if (!config) {
    return res.status(404).json({
      code: 404,
      message: 'Parameter config not found'
    })
  }

  res.json({
    code: 200,
    data: config
  })
})

// 健康检查
app.get('/api/external/health', validateApiKey, (req: any, res: any) => {
  console.log(`[MockAPI] Health check requested`)

  res.json({
    code: 200,
    data: {
      status: 'ok',
      timestamp: new Date().toISOString()
    }
  })
})

// 错误处理
app.use((err: any, req: any, res: any, next: any) => {
  console.error('[MockAPI] Error:', err)
  res.status(500).json({
    code: 500,
    message: 'Internal server error'
  })
})

// 启动服务器
if (require.main === module) {
  app.listen(PORT, () => {
    console.log(`[MockAPI] Mock API server running on http://localhost:${PORT}`)
    console.log(`[MockAPI] Available endpoints:`)
    console.log(`  GET /api/external/parameter-data/:dataId`)
    console.log(`  GET /api/external/parameter-config/:configId`)
    console.log(`  GET /api/external/health`)
    console.log(`[MockAPI] Use API key: test-api-key`)
  })
}

export default app
